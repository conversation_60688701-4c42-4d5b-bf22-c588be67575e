#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des dialogues améliorés GSCOM
Validation de toutes les améliorations d'interface
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_improved_dialogs():
    """Test complet des dialogues améliorés"""
    print("🎨 Test des dialogues améliorés GSCOM\n")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # Fenêtre de test
        window = QMainWindow()
        window.setWindowTitle("Test Dialogues Améliorés GSCOM")
        window.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # Test dialogue client amélio<PERSON>
        def test_improved_client_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernClientDialog
                dialog = ModernClientDialog(window)
                print("✅ ModernClientDialog amélioré créé")
                print("   • Contraste amélioré pour les champs")
                print("   • Polices et tailles harmonisées")
                print("   • Sections plus distinctes")
                print("   • Boutons avec couleurs cohérentes")
                result = dialog.exec_()
                print(f"📝 Résultat: {result}")
            except Exception as e:
                print(f"❌ Erreur: {e}")
                import traceback
                traceback.print_exc()
        
        # Test dialogue produit amélioré
        def test_improved_product_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernProductDialog
                from src.ui.utils.dialog_utils import load_product_dependencies
                
                categories, units = load_product_dependencies()
                dialog = ModernProductDialog(categories, units, window)
                print("✅ ModernProductDialog amélioré créé")
                print("   • Champs de saisie plus lisibles")
                print("   • Alignement corrigé")
                print("   • Séparations visuelles entre sections")
                print("   • Interface responsive")
                result = dialog.exec_()
                print(f"📝 Résultat: {result}")
            except Exception as e:
                print(f"❌ Erreur: {e}")
                import traceback
                traceback.print_exc()
        
        # Test dialogue devis amélioré
        def test_improved_quote_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernQuoteDialog
                dialog = ModernQuoteDialog(window)
                print("✅ ModernQuoteDialog amélioré créé")
                print("   • Style cohérent et moderne")
                print("   • Tableaux avec meilleur contraste")
                print("   • Boutons d'action optimisés")
                result = dialog.exec_()
                print(f"📝 Résultat: {result}")
            except Exception as e:
                print(f"❌ Erreur: {e}")
                import traceback
                traceback.print_exc()
        
        # Test des styles CSS
        def test_css_styles():
            try:
                from src.ui.styles.improved_dialog_styles import (
                    get_improved_dialog_styles,
                    get_improved_footer_styles,
                    get_improved_table_styles
                )
                
                dialog_css = get_improved_dialog_styles()
                footer_css = get_improved_footer_styles()
                table_css = get_improved_table_styles()
                
                print("✅ Styles CSS améliorés chargés")
                print(f"   • Styles dialogue: {len(dialog_css)} caractères")
                print(f"   • Styles pied de page: {len(footer_css)} caractères")
                print(f"   • Styles tableaux: {len(table_css)} caractères")
                print("   • Contraste amélioré ✓")
                print("   • Polices harmonisées ✓")
                print("   • Couleurs cohérentes ✓")
                print("   • Responsive design ✓")
                
            except Exception as e:
                print(f"❌ Erreur styles CSS: {e}")
        
        # Boutons de test avec styles améliorés
        btn_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4682ff, stop:1 #2c5aa0);
                border: 2px solid #1e3a8a;
                border-radius: 10px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: bold;
                font-size: 14px;
                padding: 12px 25px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a9cff, stop:1 #3d6bb5);
                transform: translateY(-2px);
            }
        """
        
        btn_client = QPushButton("🧑 Test Client Dialog Amélioré")
        btn_client.setStyleSheet(btn_style)
        btn_client.clicked.connect(test_improved_client_dialog)
        layout.addWidget(btn_client)
        
        btn_product = QPushButton("📦 Test Product Dialog Amélioré")
        btn_product.setStyleSheet(btn_style)
        btn_product.clicked.connect(test_improved_product_dialog)
        layout.addWidget(btn_product)
        
        btn_quote = QPushButton("📝 Test Quote Dialog Amélioré")
        btn_quote.setStyleSheet(btn_style)
        btn_quote.clicked.connect(test_improved_quote_dialog)
        layout.addWidget(btn_quote)
        
        btn_css = QPushButton("🎨 Test Styles CSS")
        btn_css.setStyleSheet(btn_style)
        btn_css.clicked.connect(test_css_styles)
        layout.addWidget(btn_css)
        
        # Style de la fenêtre principale
        window.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(25, 30, 45, 0.98),
                    stop:1 rgba(35, 40, 65, 0.98));
            }
            QWidget {
                color: #ffffff;
                font-family: 'Segoe UI', 'Arial', sans-serif;
            }
        """)
        
        window.setCentralWidget(central_widget)
        window.show()
        
        print("🎯 Fenêtre de test ouverte avec styles améliorés")
        print("📋 Améliorations implémentées:")
        print("   ✅ Contraste amélioré pour tous les champs")
        print("   ✅ Polices et tailles harmonisées")
        print("   ✅ Sections plus distinctes et colorées")
        print("   ✅ Alignement corrigé")
        print("   ✅ Boutons avec couleurs vives et cohérentes")
        print("   ✅ Séparations visuelles entre sections")
        print("   ✅ Interface responsive")
        print("   ✅ Style cohérent et moderne")
        
        # Test automatique des styles
        test_css_styles()
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Fonction principale"""
    print("🚀 Test des améliorations d'interface GSCOM\n")
    return test_improved_dialogs()

if __name__ == "__main__":
    sys.exit(main())

"""
PySide6-Fluent-Widgets
======================
A fluent design widgets library based on PySide6.

Documentation is available in the docstrings and
online at https://https://qfluentwidgets.com.

Examples are available at https://github.com/zhiyiYo/PyQt-Fluent-Widgets/tree/PySide6/examples.

:copyright: (c) 2021 by zhiyiYo.
:license: GPLv3 for non-commercial project, see README for more details.
"""

__version__ = "1.8.1"
__author__ = "zhiyiYo"

from .components import *
from .common import *
from .window import *
from ._rc import resource
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de correction des erreurs des fournisseurs
Vérifie que les erreurs montrées dans les captures d'écran sont corrigées
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_supplier_service():
    """Test du service des fournisseurs"""
    print("🧪 Test du SupplierService...")
    
    try:
        from src.bll.supplier_service import SupplierService
        from src.dal.database import db_manager
        
        # Initialiser la base de données
        db_manager.init_database()
        
        # Créer le service
        supplier_service = SupplierService()
        print("✅ SupplierService créé avec succès")
        
        # Test de récupération des fournisseurs
        suppliers = supplier_service.get_all_suppliers()
        print(f"✅ Récupération des fournisseurs: {len(suppliers)} trouvés")
        
        # Test de validation des données
        test_data = {
            'name': 'Test Fournisseur',
            'email': '<EMAIL>',
            'phone': '0123456789'
        }
        
        is_valid, message = supplier_service.validate_supplier_data(test_data)
        print(f"✅ Validation des données: {is_valid} - {message}")
        
        # Test avec données None (erreur précédente)
        test_data_none = {
            'name': None,
            'email': None,
            'phone': None
        }
        
        is_valid, message = supplier_service.validate_supplier_data(test_data_none)
        print(f"✅ Validation avec None: {is_valid} - {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans SupplierService: {e}")
        return False

def test_client_service():
    """Test du service client pour les fournisseurs"""
    print("\n🧪 Test du ClientService pour fournisseurs...")
    
    try:
        from src.bll.client_service import ClientService
        
        # Créer le service
        client_service = ClientService()
        print("✅ ClientService créé avec succès")
        
        # Test de récupération des fournisseurs via ClientService
        suppliers = client_service.get_all_suppliers()
        print(f"✅ Récupération via ClientService: {len(suppliers)} fournisseurs")
        
        # Test de recherche
        search_results = client_service.search_suppliers("test")
        print(f"✅ Recherche fournisseurs: {len(search_results)} résultats")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans ClientService: {e}")
        return False

def test_supplier_creation():
    """Test de création d'un fournisseur"""
    print("\n🧪 Test de création d'un fournisseur...")
    
    try:
        from src.bll.client_service import ClientService
        from src.dal.models.client import SupplierType
        
        client_service = ClientService()
        
        # Données de test valides
        supplier_data = {
            'name': 'Fournisseur Test Correction',
            'email': '<EMAIL>',
            'phone': '0123456789',
            'supplier_type': SupplierType.DISTRIBUTOR,
            'is_active': True
        }
        
        # Test de création
        success = client_service.create_supplier(supplier_data)
        print(f"✅ Création fournisseur: {'Réussie' if success else 'Échouée'}")
        
        if success:
            # Récupérer le fournisseur créé
            suppliers = client_service.search_suppliers("Fournisseur Test Correction")
            if suppliers:
                supplier = suppliers[0]
                print(f"✅ Fournisseur créé trouvé: {supplier.name} (ID: {supplier.id})")
                
                # Test de modification
                update_data = {
                    'name': 'Fournisseur Test Modifié',
                    'email': '<EMAIL>',
                    'phone': '0987654321',
                    'supplier_type': SupplierType.MANUFACTURER,
                    'is_active': True
                }
                
                update_success = client_service.update_supplier(supplier.id, update_data)
                print(f"✅ Modification fournisseur: {'Réussie' if update_success else 'Échouée'}")
                
                # Nettoyer - supprimer le fournisseur de test
                delete_success = client_service.delete_supplier(supplier.id)
                print(f"✅ Suppression fournisseur: {'Réussie' if delete_success else 'Échouée'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        return False

class SupplierTestWidget(QWidget):
    """Widget de test pour l'interface des fournisseurs"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface de test"""
        self.setWindowTitle("Test Correction Erreurs Fournisseurs")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Titre
        title = QLabel("🔧 Test de Correction des Erreurs Fournisseurs")
        title.setObjectName("testTitle")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            #testTitle {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin: 20px;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(52, 152, 219, 0.1),
                    stop:1 rgba(155, 89, 182, 0.1));
                border: 2px solid rgba(52, 152, 219, 0.3);
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)
        
        # Zone de résultats
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.results_text)
        
        # Boutons de test
        buttons_layout = QHBoxLayout()
        
        test_service_btn = QPushButton("🧪 Test SupplierService")
        test_service_btn.clicked.connect(self.test_supplier_service)
        test_service_btn.setStyleSheet(self.get_button_style("#3498db"))
        buttons_layout.addWidget(test_service_btn)
        
        test_client_btn = QPushButton("🧪 Test ClientService")
        test_client_btn.clicked.connect(self.test_client_service)
        test_client_btn.setStyleSheet(self.get_button_style("#2ecc71"))
        buttons_layout.addWidget(test_client_btn)
        
        test_creation_btn = QPushButton("🧪 Test Création")
        test_creation_btn.clicked.connect(self.test_creation)
        test_creation_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        buttons_layout.addWidget(test_creation_btn)
        
        test_ui_btn = QPushButton("🧪 Test Interface")
        test_ui_btn.clicked.connect(self.test_ui)
        test_ui_btn.setStyleSheet(self.get_button_style("#f39c12"))
        buttons_layout.addWidget(test_ui_btn)
        
        clear_btn = QPushButton("🗑️ Effacer")
        clear_btn.clicked.connect(self.results_text.clear)
        clear_btn.setStyleSheet(self.get_button_style("#95a5a6"))
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
        # Lancer les tests automatiquement
        QTimer.singleShot(1000, self.run_all_tests)
    
    def get_button_style(self, color):
        """Retourne le style pour un bouton"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {color}dd);
                border: 2px solid {color}aa;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                padding: 10px 15px;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background: {color};
                border-color: {color};
            }}
            QPushButton:pressed {{
                background: {color}aa;
            }}
        """
    
    def log(self, message):
        """Ajoute un message aux résultats"""
        self.results_text.append(message)
        self.results_text.ensureCursorVisible()
        QApplication.processEvents()
    
    def test_supplier_service(self):
        """Test du service des fournisseurs"""
        self.log("🧪 Test du SupplierService...")
        
        try:
            from src.bll.supplier_service import SupplierService
            from src.dal.database import db_manager
            
            # Initialiser la base de données
            db_manager.init_database()
            
            # Créer le service
            supplier_service = SupplierService()
            self.log("✅ SupplierService créé avec succès")
            
            # Test de récupération des fournisseurs
            suppliers = supplier_service.get_all_suppliers()
            self.log(f"✅ Récupération des fournisseurs: {len(suppliers)} trouvés")
            
            # Test de validation des données (erreur corrigée)
            test_data = {
                'name': 'Test Fournisseur',
                'email': '<EMAIL>',
                'phone': '0123456789'
            }
            
            is_valid, message = supplier_service.validate_supplier_data(test_data)
            self.log(f"✅ Validation des données: {is_valid} - {message}")
            
            # Test avec données None (erreur précédente corrigée)
            test_data_none = {
                'name': None,
                'email': None,
                'phone': None
            }
            
            is_valid, message = supplier_service.validate_supplier_data(test_data_none)
            self.log(f"✅ Validation avec None: {is_valid} - {message}")
            
            self.log("🎉 SupplierService: TOUS LES TESTS RÉUSSIS!")
            
        except Exception as e:
            self.log(f"❌ Erreur dans SupplierService: {e}")
    
    def test_client_service(self):
        """Test du service client"""
        self.log("\n🧪 Test du ClientService pour fournisseurs...")
        
        try:
            from src.bll.client_service import ClientService
            
            # Créer le service
            client_service = ClientService()
            self.log("✅ ClientService créé avec succès")
            
            # Test de récupération des fournisseurs via ClientService
            suppliers = client_service.get_all_suppliers()
            self.log(f"✅ Récupération via ClientService: {len(suppliers)} fournisseurs")
            
            # Test de recherche
            search_results = client_service.search_suppliers("test")
            self.log(f"✅ Recherche fournisseurs: {len(search_results)} résultats")
            
            self.log("🎉 ClientService: TOUS LES TESTS RÉUSSIS!")
            
        except Exception as e:
            self.log(f"❌ Erreur dans ClientService: {e}")
    
    def test_creation(self):
        """Test de création d'un fournisseur"""
        self.log("\n🧪 Test de création d'un fournisseur...")
        
        try:
            from src.bll.client_service import ClientService
            from src.dal.models.client import SupplierType
            
            client_service = ClientService()
            
            # Données de test valides
            supplier_data = {
                'name': 'Fournisseur Test Correction',
                'email': '<EMAIL>',
                'phone': '0123456789',
                'supplier_type': SupplierType.DISTRIBUTOR,
                'is_active': True
            }
            
            # Test de création
            success = client_service.create_supplier(supplier_data)
            self.log(f"✅ Création fournisseur: {'Réussie' if success else 'Échouée'}")
            
            self.log("🎉 Création: TESTS RÉUSSIS!")
            
        except Exception as e:
            self.log(f"❌ Erreur lors de la création: {e}")
    
    def test_ui(self):
        """Test de l'interface utilisateur"""
        self.log("\n🧪 Test de l'interface utilisateur...")
        
        try:
            from src.ui.modules.suppliers import SuppliersWidget
            
            # Créer le widget (sans l'afficher)
            suppliers_widget = SuppliersWidget()
            self.log("✅ SuppliersWidget créé avec succès")
            
            # Test de chargement des données
            suppliers_widget.load_suppliers()
            self.log("✅ Chargement des fournisseurs réussi")
            
            self.log("🎉 Interface: TOUS LES TESTS RÉUSSIS!")
            
        except Exception as e:
            self.log(f"❌ Erreur dans l'interface: {e}")
    
    def run_all_tests(self):
        """Lance tous les tests automatiquement"""
        self.log("🚀 DÉBUT DES TESTS DE CORRECTION DES ERREURS FOURNISSEURS")
        self.log("=" * 60)
        
        self.test_supplier_service()
        self.test_client_service()
        self.test_creation()
        self.test_ui()
        
        self.log("\n" + "=" * 60)
        self.log("🎉 TOUS LES TESTS TERMINÉS!")
        self.log("✅ Les erreurs des captures d'écran ont été corrigées!")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Tests en ligne de commande
    print("🚀 TESTS DE CORRECTION DES ERREURS FOURNISSEURS")
    print("=" * 60)
    
    success = True
    success &= test_supplier_service()
    success &= test_client_service()
    success &= test_supplier_creation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Les erreurs des captures d'écran ont été corrigées!")
    else:
        print("❌ Certains tests ont échoué")
    
    # Interface graphique
    test_widget = SupplierTestWidget()
    test_widget.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module tableau de bord avec statistiques réelles
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.dal.database import db_manager
from src.dal.models.client import Client
from src.dal.models.product import Product
from src.dal.models.user import User

# Import de l'applicateur de couleurs améliorées
try:
    from src.ui.styles.auto_color_applier import apply_enhanced_colors
    COLOR_ENHANCEMENT_AVAILABLE = True
except ImportError:
    COLOR_ENHANCEMENT_AVAILABLE = False

class DashboardWidget(QWidget):
    """Widget du tableau de bord avec statistiques réelles"""

    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        self.setup_ui()
        self.load_statistics()

        # Appliquer les couleurs améliorées
        if COLOR_ENHANCEMENT_AVAILABLE:
            apply_enhanced_colors(self)
            self.logger.info("Couleurs améliorées appliquées au dashboard")

        # Timer pour actualiser les statistiques
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_statistics)
        self.refresh_timer.start(30000)  # Actualiser toutes les 30 secondes

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête de bienvenue
        self.create_welcome_header(layout)

        # Cartes de statistiques
        self.create_stats_cards(layout)

        # Zone de contenu principal
        self.create_main_content(layout)

        # Actions rapides
        self.create_quick_actions(layout)

    def create_welcome_header(self, layout):
        """Crée l'en-tête de bienvenue"""
        header_frame = QFrame()
        header_frame.setObjectName("welcomeHeader")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)

        # Message de bienvenue
        welcome_layout = QVBoxLayout()

        welcome_label = QLabel(f"Bienvenue, {self.current_user.first_name} !")
        welcome_label.setObjectName("welcomeLabel")
        welcome_layout.addWidget(welcome_label)

        from datetime import datetime
        date_label = QLabel(datetime.now().strftime("Nous sommes le %A %d %B %Y"))
        date_label.setObjectName("dateLabel")
        welcome_layout.addWidget(date_label)

        header_layout.addLayout(welcome_layout)
        header_layout.addStretch()

        # Bouton d'actualisation
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("refreshButton")
        refresh_button.clicked.connect(self.load_statistics)
        header_layout.addWidget(refresh_button)

        layout.addWidget(header_frame)

    def create_stats_cards(self, layout):
        """Crée les cartes de statistiques"""
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(15)

        # Carte Chiffre d'affaires
        self.revenue_card = self.create_stat_card(
            "💰", "Chiffre d'affaires", "0,00 DA", "Ce mois", "#27ae60"
        )
        self.revenue_card.setObjectName("revenueCard")
        stats_layout.addWidget(self.revenue_card)

        # Carte Commandes
        self.orders_card = self.create_stat_card(
            "📋", "Commandes", "0", "En cours", "#3498db"
        )
        self.orders_card.setObjectName("ordersCard")
        stats_layout.addWidget(self.orders_card)

        # Carte Clients
        self.clients_card = self.create_stat_card(
            "👥", "Clients", "0", "0 actifs", "#00d4ff"
        )
        self.clients_card.setObjectName("clientsCard")
        stats_layout.addWidget(self.clients_card)

        # Carte Produits
        self.products_card = self.create_stat_card(
            "📦", "Produits", "0", "0 en stock", "#ff6b6b"
        )
        self.products_card.setObjectName("productsCard")
        stats_layout.addWidget(self.products_card)

        # Carte Factures
        self.invoices_card = self.create_stat_card(
            "🧾", "Factures", "0", "En attente", "#f39c12"
        )
        self.invoices_card.setObjectName("invoicesCard")
        stats_layout.addWidget(self.invoices_card)

        layout.addWidget(stats_frame)

    def create_stat_card(self, icon, title, value, subtitle, color):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(5)

        # En-tête avec icône et titre
        header_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setObjectName("cardIcon")
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        header_layout.addWidget(title_label)

        header_layout.addStretch()
        card_layout.addLayout(header_layout)

        # Valeur principale
        value_label = QLabel(value)
        value_label.setObjectName("cardValue")
        card_layout.addWidget(value_label)

        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("cardDescription")
        card_layout.addWidget(subtitle_label)

        card_layout.addStretch()

        return card

    def create_main_content(self, layout):
        """Crée la zone de contenu principal"""
        content_frame = QFrame()
        content_frame.setObjectName("mainContent")
        content_layout = QHBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # Colonne gauche - Informations système
        left_column = self.create_system_info()
        content_layout.addWidget(left_column, 1)

        # Colonne droite - Activité récente
        right_column = self.create_recent_activity()
        content_layout.addWidget(right_column, 1)

        layout.addWidget(content_frame)

    def create_system_info(self):
        """Crée la section d'informations système"""
        frame = QFrame()
        frame.setObjectName("infoSection")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("📊 Informations Système")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Contenu
        info_text = QLabel("""
        <b>🎉 Application GSCOM v1.0.0</b><br><br>

        <b>✅ Fonctionnalités disponibles :</b><br>
        • Gestion des clients et fournisseurs<br>
        • Catalogue produits avec catégories<br>
        • Système d'authentification sécurisé<br>
        • Interface moderne et intuitive<br>
        • Base de données SQLite intégrée<br><br>

        <b>🚀 Modules opérationnels :</b><br>
        • Tableau de bord avec statistiques<br>
        • Gestion des clients<br>
        • Gestion des produits<br>
        • Système de sécurité<br><br>

        <b>🔧 En développement :</b><br>
        • Module commercial complet<br>
        • Gestion des stocks<br>
        • Comptabilité intégrée<br>
        • Rapports et analyses
        """)
        info_text.setObjectName("infoText")
        info_text.setWordWrap(True)
        layout.addWidget(info_text)

        layout.addStretch()

        return frame

    def create_recent_activity(self):
        """Crée la section d'activité récente"""
        frame = QFrame()
        frame.setObjectName("activitySection")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("📈 Activité Récente")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Liste d'activité
        self.activity_list = QListWidget()
        self.activity_list.setObjectName("activityList")
        layout.addWidget(self.activity_list)

        return frame

    def create_quick_actions(self, layout):
        """Crée la section d'actions rapides"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setObjectName("sectionTitle")
        actions_layout.addWidget(title)

        # Première ligne de boutons - Gestion de base
        buttons_layout_1 = QHBoxLayout()

        new_client_btn = QPushButton("👥 Nouveau Client")
        new_client_btn.setObjectName("actionButton")
        new_client_btn.clicked.connect(self.new_client)
        buttons_layout_1.addWidget(new_client_btn)

        new_product_btn = QPushButton("📦 Nouveau Produit")
        new_product_btn.setObjectName("actionButton")
        new_product_btn.clicked.connect(self.new_product)
        buttons_layout_1.addWidget(new_product_btn)

        view_clients_btn = QPushButton("👁️ Voir Clients")
        view_clients_btn.setObjectName("actionButton")
        view_clients_btn.clicked.connect(self.view_clients)
        buttons_layout_1.addWidget(view_clients_btn)

        view_products_btn = QPushButton("👁️ Voir Produits")
        view_products_btn.setObjectName("actionButton")
        view_products_btn.clicked.connect(self.view_products)
        buttons_layout_1.addWidget(view_products_btn)

        actions_layout.addLayout(buttons_layout_1)

        # Deuxième ligne de boutons - Documents commerciaux
        buttons_layout_2 = QHBoxLayout()

        new_quote_btn = QPushButton("📝 Nouveau Devis")
        new_quote_btn.setObjectName("actionButton")
        new_quote_btn.clicked.connect(self.new_quote)
        buttons_layout_2.addWidget(new_quote_btn)

        new_order_btn = QPushButton("📋 Nouvelle Commande")
        new_order_btn.setObjectName("actionButton")
        new_order_btn.clicked.connect(self.new_order)
        buttons_layout_2.addWidget(new_order_btn)

        new_invoice_btn = QPushButton("🧾 Nouvelle Facture")
        new_invoice_btn.setObjectName("actionButton")
        new_invoice_btn.clicked.connect(self.new_invoice)
        buttons_layout_2.addWidget(new_invoice_btn)

        view_reports_btn = QPushButton("📈 Voir Rapports")
        view_reports_btn.setObjectName("actionButton")
        view_reports_btn.clicked.connect(self.view_reports)
        buttons_layout_2.addWidget(view_reports_btn)

        actions_layout.addLayout(buttons_layout_2)

        # Troisième ligne de boutons - Fonctionnalités avancées
        buttons_layout_3 = QHBoxLayout()

        backup_btn = QPushButton("💾 Sauvegarde")
        backup_btn.setObjectName("actionButton")
        backup_btn.clicked.connect(self.create_backup)
        buttons_layout_3.addWidget(backup_btn)

        import_data_btn = QPushButton("📥 Importer Données")
        import_data_btn.setObjectName("actionButton")
        import_data_btn.clicked.connect(self.import_data)
        buttons_layout_3.addWidget(import_data_btn)

        export_data_btn = QPushButton("📤 Exporter Données")
        export_data_btn.setObjectName("actionButton")
        export_data_btn.clicked.connect(self.export_data)
        buttons_layout_3.addWidget(export_data_btn)

        settings_btn = QPushButton("⚙️ Paramètres")
        settings_btn.setObjectName("actionButton")
        settings_btn.clicked.connect(self.open_settings)
        buttons_layout_3.addWidget(settings_btn)

        actions_layout.addLayout(buttons_layout_3)
        layout.addWidget(actions_frame)

    def load_statistics(self):
        """Charge les statistiques réelles"""
        try:
            with db_manager.get_session() as session:
                from datetime import datetime, timedelta
                from src.dal.models.commercial import Order, Invoice, DocumentStatus, InvoiceStatus

                # Période actuelle (ce mois)
                now = datetime.now()
                start_of_month = datetime(now.year, now.month, 1)

                # Chiffre d'affaires du mois
                monthly_revenue = session.query(Invoice).filter(
                    Invoice.date >= start_of_month,
                    Invoice.status.in_([InvoiceStatus.PAID, InvoiceStatus.PARTIAL])
                ).all()
                total_revenue = sum(float(inv.total or 0) for inv in monthly_revenue)

                # Commandes en cours
                pending_orders = session.query(Order).filter(
                    Order.status.in_([DocumentStatus.DRAFT, DocumentStatus.SENT, DocumentStatus.CONFIRMED])
                ).count()

                # Statistiques clients
                total_clients = session.query(Client).count()
                active_clients = session.query(Client).filter(Client.is_active == True).count()

                # Statistiques produits
                total_products = session.query(Product).count()
                products_in_stock = session.query(Product).filter(
                    Product.track_stock == True,
                    Product.current_stock > 0
                ).count()

                # Factures en attente
                pending_invoices = session.query(Invoice).filter(
                    Invoice.status.in_([InvoiceStatus.DRAFT, InvoiceStatus.SENT])
                ).count()

                # Mettre à jour les cartes
                self.update_stat_card(self.revenue_card, f"{total_revenue:,.0f} DA", "Ce mois")
                self.update_stat_card(self.orders_card, str(pending_orders), "En cours")
                self.update_stat_card(self.clients_card, str(total_clients), f"{active_clients} actifs")
                self.update_stat_card(self.products_card, str(total_products), f"{products_in_stock} en stock")
                self.update_stat_card(self.invoices_card, str(pending_invoices), "En attente")

                # Mettre à jour l'activité
                self.update_activity()

                self.logger.info("Statistiques mises à jour")

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des statistiques: {e}")

    def update_stat_card(self, card, value, subtitle):
        """Met à jour une carte de statistique"""
        # Trouver les labels dans la carte
        for child in card.findChildren(QLabel):
            if child.objectName() == "cardValue":
                child.setText(value)
            elif child.objectName() == "cardDescription":
                child.setText(subtitle)

    def update_activity(self):
        """Met à jour la liste d'activité"""
        self.activity_list.clear()

        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M")

        activities = [
            f"🔄 {current_time} - Statistiques actualisées",
            f"👤 {current_time} - Connexion de {self.current_user.username}",
            f"📊 {current_time} - Tableau de bord affiché",
            f"✅ {current_time} - Système opérationnel",
        ]

        for activity in activities:
            item = QListWidgetItem(activity)
            self.activity_list.addItem(item)

    def new_client(self):
        """Ouvre directement le formulaire moderne de nouveau client"""
        try:
            from src.ui.components.modern_dialogs import ModernClientDialog
            dialog = ModernClientDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                client_data = dialog.get_data()
                if self.save_client(client_data):
                    self.load_statistics()  # Actualiser les statistiques
                    self.update_activity_with_message("✅ Nouveau client créé avec succès")
                    from src.ui.components.notifications import NotificationManager
                    NotificationManager.success("Client créé avec succès !")
        except Exception as e:
            self.logger.error(f"Erreur lors de la création du client: {e}")
            # Fallback vers l'ancien dialogue
            try:
                from src.ui.modules.clients import ClientFormDialog
                dialog = ClientFormDialog("Nouveau Client", self)
                if dialog.exec_() == QDialog.Accepted:
                    client_data = dialog.get_data()
                    if self.save_client(client_data):
                        self.load_statistics()
                        self.update_activity_with_message("✅ Nouveau client créé avec succès")
                        from src.ui.components.notifications import NotificationManager
                        NotificationManager.success("Client créé avec succès !")
            except Exception as fallback_error:
                self.logger.error(f"Erreur fallback client: {fallback_error}")
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la création du client: {e}")

    def new_product(self):
        """Ouvre directement le formulaire moderne de nouveau produit"""
        try:
            from src.ui.components.modern_dialogs import ModernProductDialog

            # Charger les catégories et unités nécessaires
            from src.ui.utils.dialog_utils import load_product_dependencies
            categories, units = load_product_dependencies()

            dialog = ModernProductDialog(categories, units, self)
            if dialog.exec_() == QDialog.Accepted:
                product_data = dialog.get_data()
                if self.save_product(product_data):
                    self.load_statistics()  # Actualiser les statistiques
                    self.update_activity_with_message("✅ Nouveau produit créé avec succès")
                    try:
                        from src.ui.components.notifications import NotificationManager
                        NotificationManager.success("Produit créé avec succès !")
                    except:
                        pass  # Notifications optionnelles
        except Exception as e:
            self.logger.error(f"Erreur lors de la création du produit: {e}")
            # Fallback vers l'ancien dialogue
            try:
                from src.ui.modules.products import ProductFormDialog
                from src.ui.utils.dialog_utils import load_product_dependencies
                categories, units = load_product_dependencies()
                dialog = ProductFormDialog("Nouveau Produit", categories, units, self)
                if dialog.exec_() == QDialog.Accepted:
                    product_data = dialog.get_data()
                    if self.save_product(product_data):
                        self.load_statistics()
                        self.update_activity_with_message("✅ Nouveau produit créé avec succès")
                        try:
                            from src.ui.components.notifications import NotificationManager
                            NotificationManager.success("Produit créé avec succès !")
                        except:
                            pass  # Notifications optionnelles
            except Exception as fallback_error:
                self.logger.error(f"Erreur fallback produit: {fallback_error}")
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la création du produit: {e}")

    def view_clients(self):
        """Affiche la liste des clients"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("clients")

    def view_products(self):
        """Affiche la liste des produits"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("products")

    def new_quote(self):
        """Crée un nouveau devis avec interface moderne"""
        try:
            from src.ui.components.modern_dialogs import ModernQuoteDialog
            dialog = ModernQuoteDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                quote_data = dialog.get_data()
                if self.save_quote(quote_data):
                    self.load_statistics()  # Actualiser les statistiques
                    self.update_activity_with_message("✅ Nouveau devis créé avec succès")
                    from src.ui.components.notifications import NotificationManager
                    NotificationManager.success("Devis créé avec succès !")
        except Exception as e:
            self.logger.error(f"Erreur lors de la création du devis: {e}")
            # Fallback vers l'ancien dialogue
            try:
                from src.ui.modules.quotes import QuoteDialog
                dialog = QuoteDialog(self)
                if dialog.exec_() == QDialog.Accepted:
                    quote_data = dialog.get_data()
                    if self.save_quote(quote_data):
                        self.load_statistics()
                        self.update_activity_with_message("✅ Nouveau devis créé avec succès")
                        from src.ui.components.notifications import NotificationManager
                        NotificationManager.success("Devis créé avec succès !")
            except Exception as fallback_error:
                self.logger.error(f"Erreur fallback devis: {fallback_error}")
                # Fallback final : rediriger vers le module devis
                main_window = self.get_main_window()
                if main_window:
                    main_window.switch_module("quotes")

    def new_order(self):
        """Crée une nouvelle commande"""
        try:
            from src.ui.modules.orders import OrderDialog
            dialog = OrderDialog(parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_statistics()  # Actualiser les statistiques
                self.update_activity_with_message("✅ Nouvelle commande créée avec succès")
                from src.ui.components.notifications import NotificationManager
                NotificationManager.success("Commande créée avec succès !")
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la commande: {e}")
            # Fallback : rediriger vers le module commandes
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_module("orders")

    def new_invoice(self):
        """Crée une nouvelle facture"""
        try:
            from src.ui.modules.invoices import InvoiceFormDialog
            dialog = InvoiceFormDialog("Nouvelle Facture", self)
            if dialog.exec_() == QDialog.Accepted:
                invoice_data = dialog.get_data()
                if self.save_invoice(invoice_data):
                    self.load_statistics()  # Actualiser les statistiques
                    self.update_activity_with_message("✅ Nouvelle facture créée avec succès")
                    from src.ui.components.notifications import NotificationManager
                    NotificationManager.success("Facture créée avec succès !")
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la facture: {e}")
            # Fallback : rediriger vers le module factures
            main_window = self.get_main_window()
            if main_window:
                main_window.switch_module("invoices")

    def view_reports(self):
        """Affiche les rapports"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("reports")

    def create_backup(self):
        """Crée une sauvegarde de la base de données"""
        try:
            from src.services.backup_service import BackupService
            backup_service = BackupService()

            # Créer la sauvegarde
            backup_path = backup_service.create_backup()
            if backup_path:
                self.update_activity_with_message("✅ Sauvegarde créée avec succès")
                from src.ui.components.notifications import NotificationManager
                NotificationManager.success(f"Sauvegarde créée: {backup_path}")
            else:
                QMessageBox.warning(self, "Attention", "Erreur lors de la création de la sauvegarde")
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def import_data(self):
        """Ouvre le module d'import de données"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("import_export")
            self.update_activity_with_message("📥 Module d'import ouvert")

    def export_data(self):
        """Ouvre le module d'export de données"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("import_export")
            self.update_activity_with_message("📤 Module d'export ouvert")

    def open_settings(self):
        """Ouvre les paramètres de l'application"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("settings")
            self.update_activity_with_message("⚙️ Paramètres ouverts")

    def get_main_window(self):
        """Récupère la fenêtre principale"""
        widget = self
        while widget:
            if hasattr(widget, 'switch_module'):
                return widget
            widget = widget.parent()
        return None

    def save_client(self, data):
        """Sauvegarde un nouveau client"""
        try:
            with db_manager.get_session() as session:
                # Générer un code client automatique
                if not data.get('code'):
                    last_client = session.query(Client).order_by(Client.id.desc()).first()
                    next_id = (last_client.id + 1) if last_client else 1
                    data['code'] = f"CLI{next_id:06d}"

                client = Client(**data)
                session.add(client)
                session.commit()

                self.logger.info(f"Client créé: {client.name} ({client.code})")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du client: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")
            return False

    def save_product(self, data):
        """Sauvegarde un nouveau produit"""
        try:
            with db_manager.get_session() as session:
                # Générer un code produit automatique
                if not data.get('code'):
                    last_product = session.query(Product).order_by(Product.id.desc()).first()
                    next_id = (last_product.id + 1) if last_product else 1
                    data['code'] = f"PRD{next_id:06d}"

                product = Product(**data)
                session.add(product)
                session.commit()

                self.logger.info(f"Produit créé: {product.name} ({product.code})")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du produit: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")
            return False

    def load_product_dependencies(self):
        """Charge les catégories et unités pour les produits"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.product import Category, Unit

                # Charger les catégories (avec gestion d'erreur pour is_active)
                try:
                    categories = session.query(Category).filter(Category.is_active == True).all()
                except:
                    categories = session.query(Category).all()

                # Charger les unités (avec gestion d'erreur pour is_active)
                try:
                    units = session.query(Unit).all()
                except:
                    units = []

                # Créer une unité par défaut si aucune n'existe
                if not units:
                    try:
                        default_unit = Unit(
                            code="UNIT",
                            name="Unité",
                            symbol="u",
                            description="Unité par défaut"
                        )
                        session.add(default_unit)
                        session.commit()
                        units = [default_unit]
                        self.logger.info("Unité par défaut créée")
                    except Exception as e:
                        self.logger.error(f"Erreur création unité par défaut: {e}")
                        units = []

                # Créer une catégorie par défaut si aucune n'existe
                if not categories:
                    try:
                        default_category = Category(
                            code="CAT001",
                            name="Général",
                            description="Catégorie par défaut"
                        )
                        session.add(default_category)
                        session.commit()
                        categories = [default_category]
                        self.logger.info("Catégorie par défaut créée")
                    except Exception as e:
                        self.logger.error(f"Erreur création catégorie par défaut: {e}")
                        categories = []



                return categories, units

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des dépendances: {e}")
            # Retourner des listes vides en cas d'erreur
            return [], []

    def save_quote(self, data):
        """Sauvegarde un nouveau devis"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.commercial import Quote, DocumentStatus
                from datetime import datetime

                # Générer un numéro de devis automatique
                if not data.get('number'):
                    last_quote = session.query(Quote).order_by(Quote.id.desc()).first()
                    next_id = (last_quote.id + 1) if last_quote else 1
                    data['number'] = f"DEV-{datetime.now().strftime('%Y%m%d')}-{next_id:04d}"

                # Créer le devis avec les données de base
                quote_data = {
                    'number': data.get('number'),
                    'client_id': data.get('client_id'),
                    'date': data.get('date', datetime.now().date()),
                    'valid_until': data.get('valid_until'),
                    'notes': data.get('notes'),
                    'status': DocumentStatus.DRAFT
                }

                quote = Quote(**quote_data)
                session.add(quote)
                session.commit()

                self.logger.info(f"Devis créé: {quote.number}")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du devis: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")
            return False

    def save_invoice(self, data):
        """Sauvegarde une nouvelle facture"""
        try:
            with db_manager.get_session() as session:
                from src.dal.models.commercial import Invoice, InvoiceStatus
                from datetime import datetime

                # Générer un numéro de facture automatique
                if not data.get('number'):
                    last_invoice = session.query(Invoice).order_by(Invoice.id.desc()).first()
                    next_id = (last_invoice.id + 1) if last_invoice else 1
                    data['number'] = f"FAC-{datetime.now().strftime('%Y%m%d')}-{next_id:04d}"

                # Créer la facture avec les données de base
                invoice_data = {
                    'number': data.get('number'),
                    'client_id': data.get('client_id'),
                    'date': data.get('date', datetime.now().date()),
                    'due_date': data.get('due_date'),
                    'notes': data.get('notes'),
                    'status': InvoiceStatus.DRAFT
                }

                invoice = Invoice(**invoice_data)
                session.add(invoice)
                session.commit()

                self.logger.info(f"Facture créée: {invoice.number}")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde de la facture: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")
            return False

    def update_activity_with_message(self, message):
        """Ajoute un message à l'activité récente"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M")

            # Ajouter le nouveau message en haut de la liste
            item = QListWidgetItem(f"{current_time} - {message}")
            self.activity_list.insertItem(0, item)

            # Limiter à 10 éléments maximum
            while self.activity_list.count() > 10:
                self.activity_list.takeItem(self.activity_list.count() - 1)

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de l'activité: {e}")

    def trigger_new_action(self, module):
        """Déclenche l'action nouveau dans un module"""
        # Cette méthode sera appelée après le changement de module
        pass

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet("""
            #welcomeHeader {
                background: rgba(255, 255, 255, 0.08);
                border-radius: 12px;
                border: 1px solid rgba(0, 212, 255, 0.2);
            }

            #welcomeLabel {
                font-size: 32px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 8px;
            }

            #dateLabel {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
            }

            #refreshButton {
                background: rgba(0, 212, 255, 0.2);
                border: 1px solid rgba(0, 212, 255, 0.5);
                border-radius: 6px;
                padding: 8px 16px;
                color: #00d4ff;
                font-weight: 500;
            }

            #refreshButton:hover {
                background: rgba(0, 212, 255, 0.3);
            }

            #statCard {
                background: rgba(255, 255, 255, 0.08);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
            }

            #statCard:hover {
                background: rgba(255, 255, 255, 0.12);
                border-color: rgba(0, 212, 255, 0.4);
                /* transform: translateY(-2px); */
            }

            #statTitle {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                font-weight: 600;
            }

            #statValue {
                font-size: 28px;
                font-weight: bold;
                color: #ffffff;
                margin: 8px 0;
            }

            #statSubtitle {
                font-size: 13px;
                color: rgba(255, 255, 255, 0.7);
                font-weight: 500;
            }

            #infoSection, #activitySection, #actionsFrame {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }

            #sectionTitle {
                font-size: 16px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 15px;
            }

            #infoText {
                color: rgba(255, 255, 255, 0.9);
                line-height: 1.4;
            }

            #activityList {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.9);
            }

            #activityList::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            #activityList::item:hover {
                background: rgba(0, 212, 255, 0.1);
            }

            #actionButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.2),
                    stop:1 rgba(255, 0, 255, 0.2));
                border: 1px solid rgba(0, 212, 255, 0.5);
                border-radius: 8px;
                padding: 12px 20px;
                color: white;
                font-weight: 500;
                font-size: 14px;
            }

            #actionButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(255, 0, 255, 0.3));
                border-color: #00d4ff;
            }
        """)

    def showEvent(self, event):
        """Événement d'affichage"""
        super().showEvent(event)
        self.apply_styles()
        self.load_statistics()

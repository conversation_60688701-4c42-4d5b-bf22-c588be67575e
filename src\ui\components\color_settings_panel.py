#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Panneau de configuration avancé des couleurs GSCOM
Interface utilisateur pour toutes les fonctionnalités de couleur
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

try:
    from src.ui.styles.advanced_color_features import get_advanced_color_manager
    from src.ui.styles.theme_manager import get_theme_manager
    ADVANCED_FEATURES_AVAILABLE = True
except ImportError:
    ADVANCED_FEATURES_AVAILABLE = False

class ColorSettingsPanel(QDialog):
    """Panneau de configuration avancé des couleurs"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.advanced_manager = get_advanced_color_manager() if ADVANCED_FEATURES_AVAILABLE else None
        self.theme_manager = get_theme_manager() if ADVANCED_FEATURES_AVAILABLE else None
        
        self.init_ui()
        self.load_current_settings()
        self.connect_signals()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        self.setWindowTitle("⚙️ Configuration Avancée des Couleurs")
        self.setFixedSize(600, 700)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Titre
        title = QLabel("🎨 Configuration Avancée des Couleurs GSCOM")
        title.setObjectName("dialogTitle")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # Zone de défilement
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # Sections de configuration
        self.create_theme_section(scroll_layout)
        self.create_accessibility_section(scroll_layout)
        self.create_animation_section(scroll_layout)
        self.create_color_blind_section(scroll_layout)
        self.create_custom_palette_section(scroll_layout)
        self.create_import_export_section(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # Boutons
        self.create_buttons(main_layout)
        
        # Appliquer les styles
        self.apply_panel_styles()
    
    def create_theme_section(self, layout):
        """Crée la section de configuration des thèmes"""
        group = QGroupBox("🌙 Configuration des Thèmes")
        group_layout = QVBoxLayout(group)
        
        # Sélection du thème
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("Thème actuel :"))
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Sombre", "Clair", "Automatique"])
        theme_layout.addWidget(self.theme_combo)
        
        group_layout.addLayout(theme_layout)
        
        # Thème automatique
        self.auto_theme_check = QCheckBox("🕐 Basculement automatique selon l'heure")
        self.auto_theme_check.setToolTip("Thème sombre de 18h à 8h, clair de 8h à 18h")
        group_layout.addWidget(self.auto_theme_check)
        
        # Aperçu du thème
        preview_layout = QHBoxLayout()
        preview_layout.addWidget(QLabel("Aperçu :"))
        
        self.preview_button = QPushButton("🔄 Aperçu du Thème")
        self.preview_button.clicked.connect(self.preview_theme)
        preview_layout.addWidget(self.preview_button)
        
        group_layout.addLayout(preview_layout)
        
        layout.addWidget(group)
    
    def create_accessibility_section(self, layout):
        """Crée la section d'accessibilité"""
        group = QGroupBox("♿ Accessibilité Renforcée")
        group_layout = QVBoxLayout(group)
        
        # Mode accessibilité
        self.accessibility_check = QCheckBox("🔍 Mode accessibilité renforcée")
        self.accessibility_check.setToolTip("Contrastes maximaux, texte agrandi, bordures renforcées")
        group_layout.addWidget(self.accessibility_check)
        
        # Mode contraste élevé
        self.high_contrast_check = QCheckBox("⚫ Mode contraste élevé")
        self.high_contrast_check.setToolTip("Noir et blanc avec contrastes maximaux")
        group_layout.addWidget(self.high_contrast_check)
        
        # Informations d'accessibilité
        info_label = QLabel("""
        <b>Fonctionnalités d'accessibilité :</b><br>
        • Contrastes WCAG 2.1 AAA (ratio > 7:1)<br>
        • Texte agrandi et bordures renforcées<br>
        • Couleurs haute visibilité<br>
        • Focus renforcé pour la navigation clavier
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 11px; padding: 10px;")
        group_layout.addWidget(info_label)
        
        layout.addWidget(group)
    
    def create_animation_section(self, layout):
        """Crée la section des animations"""
        group = QGroupBox("⚡ Animations et Transitions")
        group_layout = QVBoxLayout(group)
        
        # Vitesse d'animation
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("Vitesse des animations :"))
        
        self.animation_slider = QSlider(Qt.Horizontal)
        self.animation_slider.setRange(1, 30)  # 0.1x à 3.0x
        self.animation_slider.setValue(10)  # 1.0x par défaut
        self.animation_slider.setTickPosition(QSlider.TicksBelow)
        self.animation_slider.setTickInterval(5)
        speed_layout.addWidget(self.animation_slider)
        
        self.speed_label = QLabel("1.0x")
        self.speed_label.setMinimumWidth(40)
        speed_layout.addWidget(self.speed_label)
        
        group_layout.addLayout(speed_layout)
        
        # Labels de vitesse
        labels_layout = QHBoxLayout()
        labels_layout.addWidget(QLabel("Lent"))
        labels_layout.addStretch()
        labels_layout.addWidget(QLabel("Normal"))
        labels_layout.addStretch()
        labels_layout.addWidget(QLabel("Rapide"))
        group_layout.addLayout(labels_layout)
        
        # Test d'animation
        self.test_animation_button = QPushButton("🎬 Tester les Animations")
        self.test_animation_button.clicked.connect(self.test_animations)
        group_layout.addWidget(self.test_animation_button)
        
        layout.addWidget(group)
    
    def create_color_blind_section(self, layout):
        """Crée la section pour les daltoniens"""
        group = QGroupBox("👁️ Support des Daltoniens")
        group_layout = QVBoxLayout(group)
        
        # Type de daltonisme
        colorblind_layout = QHBoxLayout()
        colorblind_layout.addWidget(QLabel("Type de daltonisme :"))
        
        self.colorblind_combo = QComboBox()
        self.colorblind_combo.addItems([
            "Aucun",
            "Protanopie (déficience rouge)",
            "Deutéranopie (déficience verte)",
            "Tritanopie (déficience bleue)"
        ])
        colorblind_layout.addWidget(self.colorblind_combo)
        
        group_layout.addLayout(colorblind_layout)
        
        # Informations
        info_label = QLabel("""
        <b>Palettes adaptées :</b><br>
        • Protanopie : Bleu et orange<br>
        • Deutéranopie : Bleu vif et orange vif<br>
        • Tritanopie : Rose et vert
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 11px; padding: 10px;")
        group_layout.addWidget(info_label)
        
        layout.addWidget(group)
    
    def create_custom_palette_section(self, layout):
        """Crée la section des palettes personnalisées"""
        group = QGroupBox("🎨 Palettes Personnalisées")
        group_layout = QVBoxLayout(group)
        
        # Liste des palettes
        palettes_layout = QHBoxLayout()
        palettes_layout.addWidget(QLabel("Palette :"))
        
        self.palette_combo = QComboBox()
        self.palette_combo.addItem("Palette par défaut")
        palettes_layout.addWidget(self.palette_combo)
        
        group_layout.addLayout(palettes_layout)
        
        # Boutons de gestion
        buttons_layout = QHBoxLayout()
        
        self.create_palette_button = QPushButton("➕ Créer")
        self.create_palette_button.clicked.connect(self.create_custom_palette)
        buttons_layout.addWidget(self.create_palette_button)
        
        self.edit_palette_button = QPushButton("✏️ Modifier")
        self.edit_palette_button.clicked.connect(self.edit_custom_palette)
        buttons_layout.addWidget(self.edit_palette_button)
        
        self.delete_palette_button = QPushButton("🗑️ Supprimer")
        self.delete_palette_button.clicked.connect(self.delete_custom_palette)
        buttons_layout.addWidget(self.delete_palette_button)
        
        group_layout.addLayout(buttons_layout)
        
        layout.addWidget(group)
    
    def create_import_export_section(self, layout):
        """Crée la section d'import/export"""
        group = QGroupBox("💾 Sauvegarde et Partage")
        group_layout = QVBoxLayout(group)
        
        # Boutons d'import/export
        buttons_layout = QHBoxLayout()
        
        self.export_button = QPushButton("📤 Exporter Paramètres")
        self.export_button.clicked.connect(self.export_settings)
        buttons_layout.addWidget(self.export_button)
        
        self.import_button = QPushButton("📥 Importer Paramètres")
        self.import_button.clicked.connect(self.import_settings)
        buttons_layout.addWidget(self.import_button)
        
        group_layout.addLayout(buttons_layout)
        
        # Reset
        self.reset_button = QPushButton("🔄 Réinitialiser Tout")
        self.reset_button.clicked.connect(self.reset_all_settings)
        self.reset_button.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; }")
        group_layout.addWidget(self.reset_button)
        
        layout.addWidget(group)
    
    def create_buttons(self, layout):
        """Crée les boutons de validation"""
        buttons_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("✅ Appliquer")
        self.apply_button.setObjectName("dialogAcceptButton")
        self.apply_button.clicked.connect(self.apply_settings)
        buttons_layout.addWidget(self.apply_button)
        
        self.cancel_button = QPushButton("❌ Annuler")
        self.cancel_button.setObjectName("dialogCancelButton")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def connect_signals(self):
        """Connecte les signaux"""
        if self.advanced_manager:
            self.animation_slider.valueChanged.connect(self.update_speed_label)
            self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
            self.auto_theme_check.toggled.connect(self.on_auto_theme_toggled)
    
    def load_current_settings(self):
        """Charge les paramètres actuels"""
        if not self.advanced_manager:
            return
        
        settings = self.advanced_manager.get_current_settings()
        
        # Thème
        if self.theme_manager:
            current_theme = self.theme_manager.get_current_theme()
            theme_index = {"dark": 0, "light": 1, "auto": 2}.get(current_theme, 0)
            self.theme_combo.setCurrentIndex(theme_index)
        
        # Paramètres avancés
        self.accessibility_check.setChecked(settings.get("accessibility_mode", False))
        self.high_contrast_check.setChecked(settings.get("high_contrast_mode", False))
        self.auto_theme_check.setChecked(settings.get("auto_theme_enabled", True))
        
        # Animation
        speed = settings.get("animation_speed", 1.0)
        self.animation_slider.setValue(int(speed * 10))
        self.update_speed_label()
        
        # Daltonisme
        colorblind_mode = settings.get("color_blind_mode", "none")
        colorblind_index = {"none": 0, "protanopia": 1, "deuteranopia": 2, "tritanopia": 3}.get(colorblind_mode, 0)
        self.colorblind_combo.setCurrentIndex(colorblind_index)
        
        # Palettes personnalisées
        self.load_custom_palettes()
    
    def load_custom_palettes(self):
        """Charge les palettes personnalisées"""
        if not self.advanced_manager:
            return
        
        self.palette_combo.clear()
        self.palette_combo.addItem("Palette par défaut")
        
        for palette_name in self.advanced_manager.custom_palettes.keys():
            self.palette_combo.addItem(palette_name)
    
    def update_speed_label(self):
        """Met à jour le label de vitesse"""
        value = self.animation_slider.value()
        speed = value / 10.0
        self.speed_label.setText(f"{speed:.1f}x")
    
    def on_theme_changed(self, theme_text):
        """Gère le changement de thème"""
        if not self.theme_manager:
            return
        
        theme_map = {"Sombre": "dark", "Clair": "light", "Automatique": "auto"}
        theme = theme_map.get(theme_text, "dark")
        
        # Aperçu immédiat
        self.theme_manager.set_theme(theme)
    
    def on_auto_theme_toggled(self, checked):
        """Gère l'activation du thème automatique"""
        if self.advanced_manager:
            self.advanced_manager.enable_auto_theme(checked)
    
    def preview_theme(self):
        """Aperçu du thème sélectionné"""
        self.on_theme_changed(self.theme_combo.currentText())
        QMessageBox.information(self, "Aperçu", "Thème appliqué ! Vous pouvez voir les changements.")
    
    def test_animations(self):
        """Teste les animations avec la vitesse sélectionnée"""
        speed = self.animation_slider.value() / 10.0
        if self.advanced_manager:
            self.advanced_manager.set_animation_speed(speed)
        
        # Animation de test sur le bouton
        self.test_animation_button.setText("🎬 Animation testée !")
        QTimer.singleShot(1000, lambda: self.test_animation_button.setText("🎬 Tester les Animations"))
    
    def create_custom_palette(self):
        """Crée une nouvelle palette personnalisée"""
        QMessageBox.information(self, "Palette", "Fonctionnalité de création de palette en développement")
    
    def edit_custom_palette(self):
        """Modifie une palette personnalisée"""
        QMessageBox.information(self, "Palette", "Fonctionnalité d'édition de palette en développement")
    
    def delete_custom_palette(self):
        """Supprime une palette personnalisée"""
        current_palette = self.palette_combo.currentText()
        if current_palette != "Palette par défaut":
            reply = QMessageBox.question(self, "Supprimer", f"Supprimer la palette '{current_palette}' ?")
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "Palette", "Palette supprimée")
    
    def export_settings(self):
        """Exporte les paramètres"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Exporter Paramètres", "gscom_color_settings.json", "JSON Files (*.json)"
        )
        if file_path and self.advanced_manager:
            if self.advanced_manager.export_settings(file_path):
                QMessageBox.information(self, "Export", "Paramètres exportés avec succès !")
    
    def import_settings(self):
        """Importe les paramètres"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Importer Paramètres", "", "JSON Files (*.json)"
        )
        if file_path and self.advanced_manager:
            if self.advanced_manager.import_settings(file_path):
                QMessageBox.information(self, "Import", "Paramètres importés avec succès !")
                self.load_current_settings()
    
    def reset_all_settings(self):
        """Réinitialise tous les paramètres"""
        reply = QMessageBox.question(
            self, "Réinitialiser", 
            "Réinitialiser tous les paramètres de couleur ?\nCette action est irréversible."
        )
        if reply == QMessageBox.Yes:
            if self.advanced_manager:
                self.advanced_manager.restore_normal_mode()
            QMessageBox.information(self, "Réinitialisation", "Paramètres réinitialisés !")
            self.load_current_settings()
    
    def apply_settings(self):
        """Applique tous les paramètres"""
        if not self.advanced_manager:
            QMessageBox.warning(self, "Erreur", "Gestionnaire avancé non disponible")
            return
        
        try:
            # Appliquer les paramètres
            self.advanced_manager.enable_accessibility_mode(self.accessibility_check.isChecked())
            self.advanced_manager.enable_high_contrast_mode(self.high_contrast_check.isChecked())
            self.advanced_manager.enable_auto_theme(self.auto_theme_check.isChecked())
            
            # Animation
            speed = self.animation_slider.value() / 10.0
            self.advanced_manager.set_animation_speed(speed)
            
            # Daltonisme
            colorblind_modes = ["none", "protanopia", "deuteranopia", "tritanopia"]
            mode = colorblind_modes[self.colorblind_combo.currentIndex()]
            self.advanced_manager.set_color_blind_mode(mode)
            
            self.settings_changed.emit()
            QMessageBox.information(self, "Succès", "Paramètres appliqués avec succès !")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'application : {e}")
    
    def apply_panel_styles(self):
        """Applique les styles au panneau"""
        try:
            from src.ui.styles.unified_color_system import get_complete_unified_styles
            from src.ui.styles.module_color_enhancements import get_complete_module_styles
            
            styles = get_complete_unified_styles() + get_complete_module_styles()
            self.setStyleSheet(styles)
        except ImportError:
            pass

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tableau de bord amélioré GSCOM
Interface moderne, professionnelle et ergonomique
"""

import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

try:
    from src.ui.components.base_widget import BaseWidget
except ImportError:
    BaseWidget = QWidget

try:
    from src.dal.database_manager import db_manager
except ImportError:
    db_manager = None

class EnhancedDashboardWidget(QWidget):
    """Widget du tableau de bord amélioré"""

    def __init__(self, user=None, parent=None):
        super().__init__(parent)
        self.user = user
        self.logger = logging.getLogger(__name__)
        self.dark_mode = True  # Mode sombre par défaut

        self.init_ui()
        self.apply_enhanced_styles()
        self.load_statistics()

    def init_ui(self):
        """Initialise l'interface utilisateur améliorée"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(25)

        # En-tête avec contrôles
        self.create_enhanced_header(layout)

        # Indicateurs clés améliorés
        self.create_enhanced_kpi_cards(layout)

        # Zone principale avec widgets dynamiques
        self.create_main_dashboard_area(layout)

        # Actions rapides réorganisées
        self.create_enhanced_quick_actions(layout)

    def create_enhanced_header(self, layout):
        """Crée l'en-tête amélioré avec contrôles"""
        header_frame = QFrame()
        header_frame.setObjectName("enhancedHeader")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)

        # Titre principal
        title_layout = QVBoxLayout()

        welcome_label = QLabel("🏠 Tableau de Bord GSCOM")
        welcome_label.setObjectName("enhancedTitle")
        title_layout.addWidget(welcome_label)

        date_label = QLabel(datetime.now().strftime("📅 %A %d %B %Y - %H:%M"))
        date_label.setObjectName("enhancedDate")
        title_layout.addWidget(date_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        # Contrôles en haut à droite
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)

        # Bouton mode clair/sombre
        self.theme_button = QPushButton("🌙" if self.dark_mode else "☀️")
        self.theme_button.setObjectName("themeToggle")
        self.theme_button.setFixedSize(45, 45)
        self.theme_button.setToolTip("Basculer le thème")
        self.theme_button.clicked.connect(self.toggle_theme)
        controls_layout.addWidget(self.theme_button)

        # Bouton de rafraîchissement
        refresh_button = QPushButton("🔄")
        refresh_button.setObjectName("enhancedRefresh")
        refresh_button.setFixedSize(45, 45)
        refresh_button.setToolTip("Actualiser les données")
        refresh_button.clicked.connect(self.load_statistics)
        controls_layout.addWidget(refresh_button)

        header_layout.addLayout(controls_layout)
        layout.addWidget(header_frame)

    def create_enhanced_kpi_cards(self, layout):
        """Crée les cartes d'indicateurs clés améliorées"""
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiFrame")
        kpi_layout = QHBoxLayout(kpi_frame)
        kpi_layout.setContentsMargins(0, 0, 0, 0)
        kpi_layout.setSpacing(20)

        # Données des KPI avec couleurs distinctes et icônes
        kpi_data = [
            {
                "icon": "💰",
                "title": "Chiffre d'Affaires",
                "value": "125,450 DA",
                "subtitle": "Ce mois (+12%)",
                "color": "#27ae60",
                "bg_color": "rgba(39, 174, 96, 0.15)",
                "trend": "+12%"
            },
            {
                "icon": "📋",
                "title": "Commandes",
                "value": "28",
                "subtitle": "En cours (5 nouvelles)",
                "color": "#3498db",
                "bg_color": "rgba(52, 152, 219, 0.15)",
                "trend": "+5"
            },
            {
                "icon": "👥",
                "title": "Clients",
                "value": "150",
                "subtitle": "142 actifs",
                "color": "#00d4ff",
                "bg_color": "rgba(0, 212, 255, 0.15)",
                "trend": "+8"
            },
            {
                "icon": "📦",
                "title": "Produits",
                "value": "320",
                "subtitle": "304 en stock",
                "color": "#ff6b6b",
                "bg_color": "rgba(255, 107, 107, 0.15)",
                "trend": "+15"
            },
            {
                "icon": "🧾",
                "title": "Factures",
                "value": "45",
                "subtitle": "12 en attente",
                "color": "#f39c12",
                "bg_color": "rgba(243, 156, 18, 0.15)",
                "trend": "+3"
            }
        ]

        for kpi in kpi_data:
            card = self.create_enhanced_kpi_card(kpi)
            kpi_layout.addWidget(card)

        layout.addWidget(kpi_frame)

    def create_enhanced_kpi_card(self, kpi_data):
        """Crée une carte KPI améliorée"""
        card = QFrame()
        card.setObjectName("enhancedKpiCard")
        card.setFixedHeight(140)

        # Appliquer la couleur de fond spécifique
        card.setStyleSheet(f"""
            #enhancedKpiCard {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {kpi_data['bg_color']},
                    stop:1 rgba(255, 255, 255, 0.05));
                border: 2px solid {kpi_data['color']}40;
                border-radius: 16px;
                margin: 5px;
            }}
            #enhancedKpiCard:hover {{
                border-color: {kpi_data['color']};
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)

        # En-tête avec icône et tendance
        header_layout = QHBoxLayout()

        icon_label = QLabel(kpi_data["icon"])
        icon_label.setObjectName("kpiIcon")
        icon_label.setStyleSheet(f"font-size: 32px; color: {kpi_data['color']};")
        header_layout.addWidget(icon_label)

        header_layout.addStretch()

        trend_label = QLabel(kpi_data["trend"])
        trend_label.setObjectName("kpiTrend")
        trend_label.setStyleSheet(f"""
            background: {kpi_data['color']};
            color: white;
            border-radius: 10px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: bold;
        """)
        header_layout.addWidget(trend_label)

        layout.addLayout(header_layout)

        # Valeur principale (agrandie)
        value_label = QLabel(kpi_data["value"])
        value_label.setObjectName("kpiValue")
        value_label.setStyleSheet(f"""
            font-size: 36px;
            font-weight: bold;
            color: {kpi_data['color']};
            margin: 5px 0;
        """)
        layout.addWidget(value_label)

        # Titre et sous-titre
        title_label = QLabel(kpi_data["title"])
        title_label.setObjectName("kpiTitle")
        title_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #ffffff;")
        layout.addWidget(title_label)

        subtitle_label = QLabel(kpi_data["subtitle"])
        subtitle_label.setObjectName("kpiSubtitle")
        subtitle_label.setStyleSheet("font-size: 12px; color: rgba(255, 255, 255, 0.8);")
        layout.addWidget(subtitle_label)

        return card

    def create_main_dashboard_area(self, layout):
        """Crée la zone principale avec widgets dynamiques"""
        main_frame = QFrame()
        main_frame.setObjectName("mainDashboardArea")
        main_layout = QHBoxLayout(main_frame)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)

        # Colonne gauche - Informations système réorganisées
        left_column = self.create_enhanced_system_info()
        main_layout.addWidget(left_column, 1)

        # Colonne centrale - Widgets dynamiques
        center_column = self.create_dynamic_widgets()
        main_layout.addWidget(center_column, 1)

        # Colonne droite - Activité récente + Calendrier
        right_column = self.create_enhanced_activity_calendar()
        main_layout.addWidget(right_column, 1)

        layout.addWidget(main_frame)

    def create_enhanced_system_info(self):
        """Crée la section d'informations système améliorée"""
        frame = QFrame()
        frame.setObjectName("enhancedSystemInfo")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(25, 20, 25, 20)

        # Titre avec icône
        title = QLabel("⚙️ Informations Système")
        title.setObjectName("enhancedSectionTitle")
        layout.addWidget(title)

        # Contenu en deux colonnes avec icônes
        content_layout = QGridLayout()
        content_layout.setSpacing(15)

        system_items = [
            ("🎉", "Version", "GSCOM v1.0.0"),
            ("💾", "Base de données", "SQLite"),
            ("👤", "Utilisateur", self.user.username if self.user else "Admin"),
            ("🔐", "Sécurité", "Authentification active"),
            ("📊", "Modules", "5 disponibles"),
            ("🌐", "Statut", "En ligne")
        ]

        for i, (icon, label, value) in enumerate(system_items):
            row = i // 2
            col = (i % 2) * 2

            # Icône
            icon_label = QLabel(icon)
            icon_label.setObjectName("systemIcon")
            content_layout.addWidget(icon_label, row, col)

            # Texte
            text_label = QLabel(f"<b>{label}:</b> {value}")
            text_label.setObjectName("systemText")
            content_layout.addWidget(text_label, row, col + 1)

        layout.addLayout(content_layout)
        layout.addStretch()

        return frame

    def create_dynamic_widgets(self):
        """Crée les widgets dynamiques (graphiques, camemberts, etc.)"""
        frame = QFrame()
        frame.setObjectName("dynamicWidgets")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(25, 20, 25, 20)

        # Titre
        title = QLabel("📈 Widgets Dynamiques")
        title.setObjectName("enhancedSectionTitle")
        layout.addWidget(title)

        # Mini graphique de tendance CA (simulé avec des barres)
        trend_frame = QFrame()
        trend_frame.setObjectName("trendWidget")
        trend_layout = QVBoxLayout(trend_frame)
        trend_layout.setContentsMargins(15, 15, 15, 15)

        trend_title = QLabel("📊 Tendance CA (7 derniers jours)")
        trend_title.setObjectName("widgetTitle")
        trend_layout.addWidget(trend_title)

        # Simulation d'un graphique avec des barres
        bars_layout = QHBoxLayout()
        bars_layout.setSpacing(5)
        heights = [20, 35, 25, 45, 30, 50, 40]  # Hauteurs simulées
        for height in heights:
            bar = QFrame()
            bar.setFixedSize(15, height)
            bar.setStyleSheet(f"""
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border-radius: 3px;
            """)
            bars_layout.addWidget(bar, 0, Qt.AlignBottom)

        trend_layout.addLayout(bars_layout)
        layout.addWidget(trend_frame)

        # Camembert des commandes (simulé)
        pie_frame = QFrame()
        pie_frame.setObjectName("pieWidget")
        pie_layout = QVBoxLayout(pie_frame)
        pie_layout.setContentsMargins(15, 15, 15, 15)

        pie_title = QLabel("🥧 Répartition Commandes")
        pie_title.setObjectName("widgetTitle")
        pie_layout.addWidget(pie_title)

        # Légende du camembert
        legend_layout = QVBoxLayout()
        legend_items = [
            ("En cours", "#3498db", "60%"),
            ("Terminées", "#27ae60", "30%"),
            ("En attente", "#f39c12", "10%")
        ]

        for label, color, percent in legend_items:
            item_layout = QHBoxLayout()

            color_box = QFrame()
            color_box.setFixedSize(15, 15)
            color_box.setStyleSheet(f"background: {color}; border-radius: 3px;")
            item_layout.addWidget(color_box)

            text = QLabel(f"{label}: {percent}")
            text.setObjectName("legendText")
            item_layout.addWidget(text)
            item_layout.addStretch()

            legend_layout.addLayout(item_layout)

        pie_layout.addLayout(legend_layout)
        layout.addWidget(pie_frame)

        layout.addStretch()
        return frame

    def create_enhanced_activity_calendar(self):
        """Crée la section activité récente + calendrier"""
        frame = QFrame()
        frame.setObjectName("activityCalendarFrame")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(25, 20, 25, 20)

        # Activité récente limitée à 5 éléments
        activity_title = QLabel("🕒 Activité Récente")
        activity_title.setObjectName("enhancedSectionTitle")
        layout.addWidget(activity_title)

        # Liste des 5 dernières activités
        activity_frame = QFrame()
        activity_frame.setObjectName("activityList")
        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(15, 15, 15, 15)

        recent_activities = [
            ("🧾", "Facture FAC-2024-001 créée", "Il y a 2h"),
            ("👥", "Nouveau client SARL ALPHA", "Il y a 4h"),
            ("📋", "Commande CMD-2024-015 validée", "Il y a 6h"),
            ("📦", "Stock produit P001 mis à jour", "Hier"),
            ("💰", "Paiement reçu - 15,000 DA", "Hier")
        ]

        for icon, text, time in recent_activities:
            item_layout = QHBoxLayout()

            icon_label = QLabel(icon)
            icon_label.setObjectName("activityIcon")
            icon_label.setFixedSize(25, 25)
            item_layout.addWidget(icon_label)

            text_label = QLabel(text)
            text_label.setObjectName("activityText")
            item_layout.addWidget(text_label)

            item_layout.addStretch()

            time_label = QLabel(time)
            time_label.setObjectName("activityTime")
            item_layout.addWidget(time_label)

            activity_layout.addLayout(item_layout)

        # Lien "Voir tout"
        see_all_button = QPushButton("👁️ Voir tout")
        see_all_button.setObjectName("seeAllButton")
        see_all_button.clicked.connect(self.show_all_activity)
        activity_layout.addWidget(see_all_button)

        layout.addWidget(activity_frame)

        # Mini calendrier
        calendar_title = QLabel("📅 Calendrier")
        calendar_title.setObjectName("enhancedSectionTitle")
        layout.addWidget(calendar_title)

        calendar_frame = QFrame()
        calendar_frame.setObjectName("miniCalendar")
        calendar_layout = QVBoxLayout(calendar_frame)
        calendar_layout.setContentsMargins(15, 15, 15, 15)

        # Date actuelle
        current_date = QLabel(datetime.now().strftime("%B %Y"))
        current_date.setObjectName("calendarMonth")
        current_date.setAlignment(Qt.AlignCenter)
        calendar_layout.addWidget(current_date)

        # Jours de la semaine
        days_layout = QGridLayout()
        days = ["L", "M", "M", "J", "V", "S", "D"]
        for i, day in enumerate(days):
            day_label = QLabel(day)
            day_label.setObjectName("calendarDay")
            day_label.setAlignment(Qt.AlignCenter)
            days_layout.addWidget(day_label, 0, i)

        # Dates du mois (simulation)
        today = datetime.now().day
        for week in range(1, 6):
            for day in range(7):
                date_num = (week - 1) * 7 + day + 1
                if date_num <= 31:  # Simplification
                    date_label = QLabel(str(date_num))
                    date_label.setObjectName("calendarDate")
                    if date_num == today:
                        date_label.setObjectName("calendarToday")
                    date_label.setAlignment(Qt.AlignCenter)
                    date_label.setFixedSize(25, 25)
                    days_layout.addWidget(date_label, week, day)

        calendar_layout.addLayout(days_layout)
        layout.addWidget(calendar_frame)

        layout.addStretch()
        return frame

    def create_enhanced_quick_actions(self, layout):
        """Crée les actions rapides améliorées et réorganisées"""
        actions_frame = QFrame()
        actions_frame.setObjectName("enhancedActionsFrame")
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(25, 20, 25, 20)

        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setObjectName("enhancedSectionTitle")
        actions_layout.addWidget(title)

        # Actions groupées par catégories sur 2 lignes maximum
        categories_layout = QVBoxLayout()
        categories_layout.setSpacing(20)

        # Ligne 1 - Gestion de base
        line1_layout = QHBoxLayout()
        line1_layout.setSpacing(15)

        # Catégorie Clients
        clients_group = self.create_action_group("👥 Clients", [
            ("➕ Nouveau Client", "#00d4ff", self.new_client),
            ("📋 Liste Clients", "#3498db", self.view_clients)
        ])
        line1_layout.addWidget(clients_group)

        # Catégorie Produits
        products_group = self.create_action_group("📦 Produits", [
            ("➕ Nouveau Produit", "#ff6b6b", self.new_product),
            ("📋 Catalogue", "#e74c3c", self.view_products)
        ])
        line1_layout.addWidget(products_group)

        # Catégorie Documents
        docs_group = self.create_action_group("📄 Documents", [
            ("💰 Nouveau Devis", "#27ae60", self.new_quote),
            ("🧾 Nouvelle Facture", "#f39c12", self.new_invoice)
        ])
        line1_layout.addWidget(docs_group)

        categories_layout.addLayout(line1_layout)

        # Ligne 2 - Fonctionnalités avancées
        line2_layout = QHBoxLayout()
        line2_layout.setSpacing(15)

        # Catégorie Données
        data_group = self.create_action_group("💾 Données", [
            ("📤 Export", "#9b59b6", self.export_data),
            ("📥 Import", "#8e44ad", self.import_data)
        ])
        line2_layout.addWidget(data_group)

        # Catégorie Rapports
        reports_group = self.create_action_group("📊 Rapports", [
            ("📈 Statistiques", "#34495e", self.view_reports),
            ("💼 Comptabilité", "#2c3e50", self.view_accounting)
        ])
        line2_layout.addWidget(reports_group)

        # Catégorie Système
        system_group = self.create_action_group("⚙️ Système", [
            ("💾 Sauvegarde", "#16a085", self.backup_data),
            ("🔧 Paramètres", "#7f8c8d", self.open_settings)
        ])
        line2_layout.addWidget(system_group)

        categories_layout.addLayout(line2_layout)
        actions_layout.addLayout(categories_layout)

        layout.addWidget(actions_frame)

    def create_action_group(self, title, actions):
        """Crée un groupe d'actions"""
        group_frame = QFrame()
        group_frame.setObjectName("actionGroup")
        group_layout = QVBoxLayout(group_frame)
        group_layout.setContentsMargins(15, 15, 15, 15)
        group_layout.setSpacing(10)

        # Titre du groupe
        group_title = QLabel(title)
        group_title.setObjectName("actionGroupTitle")
        group_layout.addWidget(group_title)

        # Boutons d'action
        for action_text, color, callback in actions:
            button = QPushButton(action_text)
            button.setObjectName("enhancedActionButton")
            button.setFixedHeight(40)
            button.setStyleSheet(f"""
                QPushButton#enhancedActionButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:1 {color}CC);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    font-size: 13px;
                }}
                QPushButton#enhancedActionButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}DD, stop:1 {color}AA);
                    transform: translateY(-1px);
                }}
                QPushButton#enhancedActionButton:pressed {{
                    transform: translateY(0px);
                }}
            """)
            button.clicked.connect(callback)
            group_layout.addWidget(button)

        return group_frame

    # Méthodes de fonctionnalité
    def toggle_theme(self):
        """Bascule entre mode sombre et clair"""
        self.dark_mode = not self.dark_mode
        self.theme_button.setText("🌙" if self.dark_mode else "☀️")
        self.apply_enhanced_styles()

    def load_statistics(self):
        """Charge les statistiques réelles depuis la base de données"""
        try:
            if db_manager:
                with db_manager.get_session() as session:
                    # Ici vous pouvez charger les vraies données
                    # Pour l'instant, on simule
                    self.logger.info("Statistiques actualisées depuis la base de données")
            else:
                # Mode simulation sans base de données
                self.logger.info("Statistiques simulées (base de données non disponible)")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des statistiques: {e}")

    def show_all_activity(self):
        """Affiche toute l'activité"""
        QMessageBox.information(self, "Activité", "Fonctionnalité en cours de développement")

    # Méthodes d'action rapide
    def new_client(self):
        """Nouveau client"""
        try:
            from src.ui.modules.clients import ClientDialog
            dialog = ClientDialog(parent=self)
            dialog.exec_()
        except:
            QMessageBox.information(self, "Client", "Module clients en cours de développement")

    def view_clients(self):
        """Voir les clients"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("clients")

    def new_product(self):
        """Nouveau produit"""
        try:
            from src.ui.modules.products import ProductDialog
            dialog = ProductDialog(parent=self)
            dialog.exec_()
        except:
            QMessageBox.information(self, "Produit", "Module produits en cours de développement")

    def view_products(self):
        """Voir les produits"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("products")

    def new_quote(self):
        """Nouveau devis"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("commercial")

    def new_invoice(self):
        """Nouvelle facture"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("invoices")

    def export_data(self):
        """Export de données"""
        QMessageBox.information(self, "Export", "Fonctionnalité d'export en cours de développement")

    def import_data(self):
        """Import de données"""
        QMessageBox.information(self, "Import", "Fonctionnalité d'import en cours de développement")

    def view_reports(self):
        """Voir les rapports"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("reports")

    def view_accounting(self):
        """Voir la comptabilité"""
        QMessageBox.information(self, "Comptabilité", "Module comptabilité en cours de développement")

    def backup_data(self):
        """Sauvegarde des données"""
        QMessageBox.information(self, "Sauvegarde", "Fonctionnalité de sauvegarde en cours de développement")

    def open_settings(self):
        """Ouvrir les paramètres"""
        QMessageBox.information(self, "Paramètres", "Module paramètres en cours de développement")

    def get_main_window(self):
        """Récupère la fenêtre principale"""
        widget = self
        while widget:
            if hasattr(widget, 'switch_module'):
                return widget
            widget = widget.parent()
        return None

    def apply_enhanced_styles(self):
        """Applique les styles CSS améliorés"""
        # Couleurs selon le thème
        if self.dark_mode:
            bg_primary = "rgba(18, 24, 38, 0.98)"
            bg_secondary = "rgba(25, 35, 55, 0.98)"
            text_primary = "#ffffff"
            text_secondary = "rgba(255, 255, 255, 0.8)"
            border_color = "rgba(255, 255, 255, 0.15)"
            accent_color = "#00d4ff"
        else:
            bg_primary = "rgba(248, 250, 252, 0.98)"
            bg_secondary = "rgba(255, 255, 255, 0.98)"
            text_primary = "#2c3e50"
            text_secondary = "rgba(44, 62, 80, 0.8)"
            border_color = "rgba(0, 0, 0, 0.1)"
            accent_color = "#3498db"

        self.setStyleSheet(f"""
            /* ===== FOND PRINCIPAL ===== */
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {bg_primary},
                    stop:1 {bg_secondary});
                color: {text_primary};
                font-family: 'Segoe UI', 'Inter', 'Roboto', sans-serif;
            }}

            /* ===== EN-TÊTE AMÉLIORÉ ===== */
            #enhancedHeader {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.12),
                    stop:1 rgba(255, 255, 255, 0.06));
                border: 2px solid {border_color};
                border-radius: 20px;
                margin-bottom: 10px;
            }}

            #enhancedTitle {{
                font-size: 36px;
                font-weight: bold;
                color: {accent_color};
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                margin-bottom: 5px;
            }}

            #enhancedDate {{
                font-size: 16px;
                color: {text_secondary};
                font-weight: 500;
            }}

            /* ===== BOUTONS DE CONTRÔLE ===== */
            #themeToggle, #enhancedRefresh {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {accent_color}, stop:1 {accent_color}CC);
                border: 2px solid {accent_color};
                border-radius: 22px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }}

            #themeToggle:hover, #enhancedRefresh:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {accent_color}DD, stop:1 {accent_color}AA);
                transform: scale(1.05);
            }}

            /* ===== CARTES KPI ===== */
            #kpiFrame {{
                background: transparent;
                border: none;
                margin: 10px 0;
            }}

            /* ===== SECTIONS PRINCIPALES ===== */
            #enhancedSystemInfo, #dynamicWidgets, #activityCalendarFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.04));
                border: 2px solid {border_color};
                border-radius: 18px;
                margin: 5px;
            }}

            #enhancedSectionTitle {{
                font-size: 20px;
                font-weight: bold;
                color: {accent_color};
                margin-bottom: 15px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            }}

            /* ===== INFORMATIONS SYSTÈME ===== */
            #systemIcon {{
                font-size: 18px;
                margin-right: 10px;
            }}

            #systemText {{
                color: {text_primary};
                font-size: 14px;
                font-weight: 500;
                line-height: 1.4;
            }}

            /* ===== WIDGETS DYNAMIQUES ===== */
            #trendWidget, #pieWidget {{
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid {border_color};
                border-radius: 12px;
                margin: 10px 0;
            }}

            #widgetTitle {{
                font-size: 14px;
                font-weight: 600;
                color: {text_primary};
                margin-bottom: 10px;
            }}

            #legendText {{
                font-size: 12px;
                color: {text_secondary};
                font-weight: 500;
            }}

            /* ===== ACTIVITÉ RÉCENTE ===== */
            #activityList {{
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid {border_color};
                border-radius: 12px;
                margin: 10px 0;
            }}

            #activityIcon {{
                font-size: 16px;
                margin-right: 10px;
            }}

            #activityText {{
                color: {text_primary};
                font-size: 13px;
                font-weight: 500;
            }}

            #activityTime {{
                color: {text_secondary};
                font-size: 11px;
                font-style: italic;
            }}

            #seeAllButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {accent_color}40, stop:1 {accent_color}20);
                border: 1px solid {accent_color}60;
                border-radius: 8px;
                color: {accent_color};
                font-weight: 600;
                padding: 8px 16px;
                margin-top: 10px;
            }}

            #seeAllButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {accent_color}60, stop:1 {accent_color}40);
            }}

            /* ===== MINI CALENDRIER ===== */
            #miniCalendar {{
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid {border_color};
                border-radius: 12px;
                margin: 10px 0;
            }}

            #calendarMonth {{
                font-size: 16px;
                font-weight: bold;
                color: {accent_color};
                margin-bottom: 10px;
            }}

            #calendarDay {{
                font-size: 11px;
                font-weight: bold;
                color: {text_secondary};
                margin: 2px;
            }}

            #calendarDate {{
                font-size: 11px;
                color: {text_primary};
                border-radius: 12px;
                margin: 1px;
            }}

            #calendarDate:hover {{
                background: {accent_color}30;
                color: {accent_color};
            }}

            #calendarToday {{
                background: {accent_color};
                color: white;
                font-weight: bold;
                border-radius: 12px;
            }}

            /* ===== ACTIONS RAPIDES ===== */
            #enhancedActionsFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.04));
                border: 2px solid {border_color};
                border-radius: 18px;
                margin: 10px 0;
            }}

            #actionGroup {{
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid {border_color};
                border-radius: 12px;
                margin: 5px;
            }}

            #actionGroupTitle {{
                font-size: 14px;
                font-weight: bold;
                color: {accent_color};
                margin-bottom: 8px;
                text-align: center;
            }}

            /* ===== ANIMATIONS ET TRANSITIONS ===== */
            QFrame, QPushButton {{
                transition: all 0.3s ease;
            }}

            QFrame:hover {{
                transform: translateY(-2px);
            }}

            /* ===== BARRES DE DÉFILEMENT ===== */
            QScrollBar:vertical {{
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }}

            QScrollBar::handle:vertical {{
                background: {accent_color}80;
                border-radius: 6px;
                min-height: 20px;
            }}

            QScrollBar::handle:vertical:hover {{
                background: {accent_color};
            }}
        """)

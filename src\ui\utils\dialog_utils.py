#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilitaires pour les dialogues
"""

import logging

def load_product_dependencies():
    """Charge les catégories et unités pour les produits"""
    logger = logging.getLogger(__name__)

    try:
        from src.dal.database import db_manager
        from src.dal.models.product import Category, Unit

        with db_manager.get_session() as session:
            # Charger les catégories
            try:
                categories = session.query(Category).filter(Category.is_active == True).all()
            except:
                categories = session.query(Category).all()

            # Charger les unités
            try:
                units = session.query(Unit).all()
            except:
                units = []

            # Créer une unité par défaut si aucune n'existe
            if not units:
                try:
                    default_unit = Unit(
                        code="UNIT",
                        name="Unité",
                        symbol="u",
                        description="Unité par défaut"
                    )
                    session.add(default_unit)
                    session.commit()
                    units = [default_unit]
                    logger.info("Unité par défaut créée")
                except Exception as e:
                    logger.error(f"Erreur création unité par défaut: {e}")
                    units = []

            # Créer une catégorie par défaut si aucune n'existe
            if not categories:
                try:
                    default_category = Category(
                        code="CAT001",
                        name="Général",
                        description="Catégorie par défaut"
                    )
                    session.add(default_category)
                    session.commit()
                    categories = [default_category]
                    logger.info("Catégorie par défaut créée")
                except Exception as e:
                    logger.error(f"Erreur création catégorie par défaut: {e}")
                    categories = []

            # Convertir en dictionnaires pour éviter les problèmes de session
            categories_data = []
            for cat in categories:
                categories_data.append({
                    'id': cat.id,
                    'name': cat.name,
                    'code': getattr(cat, 'code', ''),
                    'description': getattr(cat, 'description', '')
                })

            units_data = []
            for unit in units:
                units_data.append({
                    'id': unit.id,
                    'name': unit.name,
                    'symbol': getattr(unit, 'symbol', ''),
                    'code': getattr(unit, 'code', ''),
                    'description': getattr(unit, 'description', '')
                })

            return categories_data, units_data

    except Exception as e:
        logger.error(f"Erreur lors du chargement des dépendances: {e}")
        # Retourner des listes vides en cas d'erreur
        return [], []

def load_clients():
    """Charge la liste des clients actifs"""
    logger = logging.getLogger(__name__)

    try:
        from src.dal.database import db_manager
        from src.dal.models.client import Client

        with db_manager.get_session() as session:
            try:
                clients = session.query(Client).filter(Client.is_active == True).all()
            except:
                clients = session.query(Client).all()

            return clients

    except Exception as e:
        logger.error(f"Erreur lors du chargement des clients: {e}")
        return []

def load_suppliers():
    """Charge la liste des fournisseurs actifs"""
    logger = logging.getLogger(__name__)

    try:
        from src.dal.database import db_manager
        from src.dal.models.client import Supplier

        with db_manager.get_session() as session:
            try:
                suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
            except:
                suppliers = session.query(Supplier).all()

            return suppliers

    except Exception as e:
        logger.error(f"Erreur lors du chargement des fournisseurs: {e}")
        return []

def generate_next_code(model_class, prefix="", session=None):
    """Génère le prochain code automatique pour un modèle"""
    logger = logging.getLogger(__name__)

    try:
        from src.dal.database import db_manager

        use_session = session or db_manager.get_session()

        # Récupérer le dernier enregistrement
        last_record = use_session.query(model_class).order_by(model_class.id.desc()).first()
        next_id = (last_record.id + 1) if last_record else 1

        # Générer le code avec préfixe
        if prefix:
            code = f"{prefix}{next_id:06d}"
        else:
            code = f"{next_id:06d}"

        if not session:
            use_session.close()

        return code

    except Exception as e:
        logger.error(f"Erreur génération code: {e}")
        return f"{prefix}000001" if prefix else "000001"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test complet de toutes les améliorations de couleurs GSCOM
Validation du système unifié, navigation, dashboard et modules
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class CompleteEnhancementTestWidget(QWidget):
    """Widget de test pour toutes les améliorations"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_all_enhancements()
    
    def init_ui(self):
        """Initialise l'interface de test complète"""
        self.setWindowTitle("GSCOM - Test Complet des Améliorations")
        self.setGeometry(50, 50, 1400, 900)
        
        # Layout principal
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Navigation améliorée
        self.create_enhanced_navigation(main_layout)
        
        # Zone de contenu avec onglets
        self.create_tabbed_content(main_layout)
    
    def create_enhanced_navigation(self, main_layout):
        """Crée la navigation avec toutes les améliorations"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(280)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Header amélioré
        header = QFrame()
        header.setObjectName("sidebarHeader")
        header.setFixedHeight(120)
        
        header_layout = QVBoxLayout(header)
        header_layout.setContentsMargins(20, 20, 20, 20)
        
        logo = QLabel("🏢")
        logo.setObjectName("logoLabel")
        logo.setFixedSize(60, 60)
        logo.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo, 0, Qt.AlignCenter)
        
        title = QLabel("GSCOM")
        title.setObjectName("titleLabel")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)
        
        subtitle = QLabel("Test Complet")
        subtitle.setObjectName("subtitleLabel")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)
        
        layout.addWidget(header)
        
        # Navigation avec couleurs améliorées
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(0, 10, 0, 10)
        
        modules = [
            ("📊", "Dashboard", True),
            ("💼", "Commercial", False),
            ("👥", "Clients", False),
            ("🏭", "Fournisseurs", False),
            ("📦", "Produits", False),
            ("📋", "Stock", False),
            ("💰", "Comptabilité", False),
            ("📈", "Rapports", False),
            ("⚙️", "Paramètres", False),
        ]
        
        for icon, title, is_active in modules:
            button = self.create_nav_button(icon, title, is_active)
            nav_layout.addWidget(button)
        
        nav_layout.addStretch()
        layout.addWidget(nav_widget)
        
        # Zone utilisateur améliorée
        user_info = QFrame()
        user_info.setObjectName("userInfo")
        user_info.setFixedHeight(100)
        
        user_layout = QVBoxLayout(user_info)
        user_layout.setContentsMargins(20, 15, 20, 15)
        
        user_name = QLabel("👤 Test Utilisateur")
        user_name.setObjectName("userName")
        user_layout.addWidget(user_name)
        
        user_role = QLabel("Administrateur")
        user_role.setObjectName("userRole")
        user_layout.addWidget(user_role)
        
        theme_button = QPushButton("🌙 Basculer Thème")
        theme_button.setObjectName("userActionButton")
        theme_button.clicked.connect(self.toggle_theme)
        user_layout.addWidget(theme_button)
        
        layout.addWidget(user_info)
        main_layout.addWidget(sidebar)
    
    def create_nav_button(self, icon, title, is_active=False):
        """Crée un bouton de navigation amélioré"""
        button = QPushButton()
        button.setObjectName("activeNavButton" if is_active else "navButton")
        button.setFixedHeight(50)
        button.setCursor(Qt.PointingHandCursor)
        
        button_layout = QHBoxLayout(button)
        button_layout.setContentsMargins(15, 10, 15, 10)
        button_layout.setSpacing(15)
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        button_layout.addWidget(title_label)
        
        button_layout.addStretch()
        return button
    
    def create_tabbed_content(self, main_layout):
        """Crée la zone de contenu avec onglets"""
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre principal
        title = QLabel("🎨 Test Complet des Améliorations GSCOM")
        title.setObjectName("contentTitle")
        title.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(title)
        
        # Onglets de test
        tab_widget = QTabWidget()
        tab_widget.setObjectName("testTabs")
        
        # Onglet Dashboard
        dashboard_tab = self.create_dashboard_test()
        tab_widget.addTab(dashboard_tab, "📊 Dashboard")
        
        # Onglet Dialogues
        dialog_tab = self.create_dialog_test()
        tab_widget.addTab(dialog_tab, "💬 Dialogues")
        
        # Onglet Tableaux
        table_tab = self.create_table_test()
        tab_widget.addTab(table_tab, "📋 Tableaux")
        
        # Onglet Boutons
        button_tab = self.create_button_test()
        tab_widget.addTab(button_tab, "🔘 Boutons")
        
        content_layout.addWidget(tab_widget)
        main_layout.addWidget(content_area)
    
    def create_dashboard_test(self):
        """Crée l'onglet de test du dashboard"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre de section
        title = QLabel("📊 Test des Cartes Dashboard")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Cartes de test
        cards_layout = QHBoxLayout()
        
        # Carte Revenue
        revenue_card = self.create_test_card("💰", "Chiffre d'affaires", "125,450 DA", "Ce mois")
        revenue_card.setObjectName("revenueCard")
        cards_layout.addWidget(revenue_card)
        
        # Carte Orders
        orders_card = self.create_test_card("📋", "Commandes", "23", "En cours")
        orders_card.setObjectName("ordersCard")
        cards_layout.addWidget(orders_card)
        
        # Carte Clients
        clients_card = self.create_test_card("👥", "Clients", "156", "12 actifs")
        clients_card.setObjectName("clientsCard")
        cards_layout.addWidget(clients_card)
        
        layout.addLayout(cards_layout)
        
        # Informations
        info = QLabel("""
        ✅ Cartes avec couleurs spécifiques par type
        ✅ Bordures colorées selon le contenu
        ✅ Effets hover avec animations
        ✅ Icônes et valeurs bien contrastées
        """)
        info.setObjectName("infoText")
        layout.addWidget(info)
        
        layout.addStretch()
        return widget
    
    def create_test_card(self, icon, title, value, subtitle):
        """Crée une carte de test"""
        card = QFrame()
        card.setObjectName("dashboardCard")
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 10, 15, 10)
        
        # Header
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("cardIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        card_layout.addLayout(header_layout)
        
        # Value
        value_label = QLabel(value)
        value_label.setObjectName("cardValue")
        card_layout.addWidget(value_label)
        
        # Subtitle
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("cardDescription")
        card_layout.addWidget(subtitle_label)
        
        return card
    
    def create_dialog_test(self):
        """Crée l'onglet de test des dialogues"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("💬 Test des Éléments de Dialogue")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Groupe de champs
        group = QGroupBox("Informations Client")
        group_layout = QFormLayout(group)
        
        # Champs de test
        name_field = QLineEdit()
        name_field.setPlaceholderText("Nom du client")
        group_layout.addRow(QLabel("Nom :"), name_field)
        
        email_field = QLineEdit()
        email_field.setPlaceholderText("<EMAIL>")
        group_layout.addRow(QLabel("Email :"), email_field)
        
        phone_field = QLineEdit()
        phone_field.setPlaceholderText("+213 XX XX XX XX")
        group_layout.addRow(QLabel("Téléphone :"), phone_field)
        
        layout.addWidget(group)
        
        # Boutons de dialogue
        buttons_layout = QHBoxLayout()
        
        accept_btn = QPushButton("✅ Valider")
        accept_btn.setObjectName("dialogAcceptButton")
        buttons_layout.addWidget(accept_btn)
        
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.setObjectName("dialogCancelButton")
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return widget
    
    def create_table_test(self):
        """Crée l'onglet de test des tableaux"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("📋 Test des Tableaux")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Tableau de test
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["ID", "Nom", "Email", "Statut"])
        
        # Données de test
        test_data = [
            ["001", "Client A", "<EMAIL>", "Actif"],
            ["002", "Client B", "<EMAIL>", "Inactif"],
            ["003", "Client C", "<EMAIL>", "Actif"],
            ["004", "Client D", "<EMAIL>", "Actif"],
            ["005", "Client E", "<EMAIL>", "Suspendu"],
        ]
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                table.setItem(row, col, QTableWidgetItem(value))
        
        layout.addWidget(table)
        return widget
    
    def create_button_test(self):
        """Crée l'onglet de test des boutons"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("🔘 Test des Boutons")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)
        
        # Boutons primaires
        primary_layout = QHBoxLayout()
        
        primary_btn = QPushButton("🔵 Bouton Principal")
        primary_btn.setObjectName("primaryButton")
        primary_layout.addWidget(primary_btn)
        
        accept_btn = QPushButton("✅ Accepter")
        accept_btn.setObjectName("acceptButton")
        primary_layout.addWidget(accept_btn)
        
        layout.addLayout(primary_layout)
        
        # Boutons secondaires
        secondary_layout = QHBoxLayout()
        
        secondary_btn = QPushButton("⚪ Bouton Secondaire")
        secondary_btn.setObjectName("secondaryButton")
        secondary_layout.addWidget(secondary_btn)
        
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.setObjectName("cancelButton")
        secondary_layout.addWidget(cancel_btn)
        
        layout.addLayout(secondary_layout)
        
        # Boutons d'action
        action_layout = QHBoxLayout()
        
        for i, (icon, text) in enumerate([("⚡", "Action 1"), ("🔧", "Action 2"), ("📊", "Action 3")]):
            btn = QPushButton(f"{icon} {text}")
            btn.setObjectName("actionButton")
            action_layout.addWidget(btn)
        
        layout.addLayout(action_layout)
        layout.addStretch()
        
        return widget
    
    def toggle_theme(self):
        """Bascule le thème"""
        try:
            from src.ui.styles.theme_manager import get_theme_manager
            theme_manager = get_theme_manager()
            current = theme_manager.get_current_theme()
            new_theme = "light" if current == "dark" else "dark"
            theme_manager.set_theme(new_theme)
            QMessageBox.information(self, "Thème", f"Thème basculé vers: {new_theme}")
        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Erreur de basculement: {e}")
    
    def apply_all_enhancements(self):
        """Applique toutes les améliorations de couleurs"""
        try:
            from src.ui.styles.unified_color_system import get_complete_unified_styles
            from src.ui.styles.module_color_enhancements import get_complete_module_styles
            
            unified_styles = get_complete_unified_styles()
            module_styles = get_complete_module_styles()
            complete_styles = unified_styles + module_styles + self.get_additional_styles()
            
            self.setStyleSheet(complete_styles)
            
        except ImportError:
            # Fallback avec styles de base
            self.setStyleSheet(self.get_fallback_styles())
    
    def get_additional_styles(self):
        """Styles additionnels pour le test"""
        return """
            #contentArea {
                background: rgba(26, 26, 46, 0.8);
            }
            
            #contentTitle {
                font-size: 28px;
                font-weight: bold;
                color: #4682ff;
                margin-bottom: 25px;
            }
            
            #testTabs {
                background: transparent;
            }
            
            #testTabs::pane {
                border: 2px solid rgba(70, 130, 255, 0.3);
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.05);
            }
            
            #testTabs::tab-bar {
                alignment: center;
            }
            
            #testTabs QTabBar::tab {
                background: rgba(70, 130, 255, 0.2);
                color: #ffffff;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                font-weight: 600;
            }
            
            #testTabs QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4682ff, stop:1 #2c5aa0);
            }
            
            #testTabs QTabBar::tab:hover {
                background: rgba(70, 130, 255, 0.4);
            }
        """
    
    def get_fallback_styles(self):
        """Styles de fallback"""
        return """
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(20, 25, 40, 0.98),
                    stop:1 rgba(25, 30, 50, 0.98));
                color: #ffffff;
                font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            }
            
            #sidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(15, 20, 35, 0.98),
                    stop:1 rgba(20, 25, 45, 0.98));
                border-right: 2px solid rgba(70, 130, 255, 0.3);
            }
            
            #navTitle {
                font-size: 15px;
                color: #ffffff;
                font-weight: 600;
            }
            
            #navIcon {
                font-size: 22px;
                color: #4682ff;
                font-weight: bold;
            }
        """

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Créer le widget de test complet
    test_widget = CompleteEnhancementTestWidget()
    test_widget.show()
    
    print("🎨 Test Complet des Améliorations GSCOM")
    print("✅ Navigation avec couleurs améliorées")
    print("✅ Dashboard avec cartes colorées")
    print("✅ Dialogues avec styles unifiés")
    print("✅ Tableaux avec en-têtes colorés")
    print("✅ Boutons avec styles cohérents")
    print("✅ Système de thème unifié")
    print("📝 Interface de test complète ouverte !")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())

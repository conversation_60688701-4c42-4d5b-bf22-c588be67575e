
def fix_theme_button(main_window):
    """Corrige le bouton de thème pour l'accessibilité"""
    from PyQt5.QtWidgets import QPushButton
    from src.ui.styles.accessibility_manager import get_accessibility_manager
    
    accessibility_manager = get_accessibility_manager()
    
    # Chercher le bouton de thème existant
    theme_button = None
    if hasattr(main_window, 'user_info'):
        for child in main_window.user_info.findChildren(QPushButton):
            if child.text() in ["🌙", "☀️", "🔄"] or "thème" in child.toolTip().lower():
                theme_button = child
                break
    
    if theme_button:
        # Reconnecter le bouton
        theme_button.clicked.disconnect()
        theme_button.clicked.connect(lambda: toggle_accessible_theme_with_feedback(main_window))
        
        # Mettre à jour l'apparence
        update_theme_button_appearance(theme_button, accessibility_manager.get_current_theme())
        
        print("✅ Bouton de thème corrigé")
    else:
        print("⚠️ Bouton de thème non trouvé")

def toggle_accessible_theme_with_feedback(main_window):
    """Bascule le thème avec feedback utilisateur"""
    from src.ui.styles.accessibility_manager import get_accessibility_manager
    
    accessibility_manager = get_accessibility_manager()
    new_theme = accessibility_manager.toggle_theme()
    
    # Mettre à jour le bouton
    theme_button = None
    if hasattr(main_window, 'user_info'):
        for child in main_window.user_info.findChildren(QPushButton):
            if child.text() in ["🌙", "☀️"] or "thème" in child.toolTip().lower():
                theme_button = child
                break
    
    if theme_button:
        update_theme_button_appearance(theme_button, new_theme)
    
    # Afficher notification
    if hasattr(main_window, 'statusBar'):
        status_bar = main_window.statusBar()
        if status_bar:
            display_name = accessibility_manager.get_theme_display_name()
            status_bar.showMessage(f"Thème changé: {display_name}", 3000)

def update_theme_button_appearance(button, theme):
    """Met à jour l'apparence du bouton de thème"""
    if theme == "dark":
        button.setText("🌙")
        button.setToolTip("Thème actuel: 🌙 Sombre\nCliquer pour passer au mode clair")
    else:
        button.setText("☀️")
        button.setToolTip("Thème actuel: ☀️ Clair\nCliquer pour passer au mode sombre")

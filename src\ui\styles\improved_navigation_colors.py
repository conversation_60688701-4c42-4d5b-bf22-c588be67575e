#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Couleurs améliorées pour le navigateur vertical GSCOM
Amélioration de la lisibilité et du contraste
"""

def get_improved_navigation_styles():
    """Retourne les styles CSS améliorés pour le navigateur vertical"""
    return """
        /* ========================================
           NAVIGATEUR VERTICAL AMÉLIORÉ
           Couleurs optimisées pour la lisibilité
           ======================================== */
        
        /* ===== SIDEBAR PRINCIPALE ===== */
        #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(20, 25, 40, 0.98),
                stop:1 rgba(25, 30, 50, 0.98));
            border-right: 2px solid rgba(70, 130, 255, 0.3);
            min-width: 280px;
            max-width: 280px;
        }

        /* ===== EN-TÊTE SIDEBAR ===== */
        #sidebarHeader {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.15),
                stop:1 rgba(70, 130, 255, 0.08));
            border-bottom: 2px solid rgba(70, 130, 255, 0.3);
            padding: 20px;
        }

        #logoLabel {
            font-size: 28px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #4682ff, stop:1 #6fa8ff);
            border-radius: 25px;
            text-align: center;
            border: 2px solid rgba(70, 130, 255, 0.6);
            color: #ffffff;
            font-weight: bold;
        }

        #titleLabel {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            margin-top: 8px;
        }

        #subtitleLabel {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* ===== BOUTONS DE NAVIGATION AMÉLIORÉS ===== */
        #navButton {
            background: transparent;
            border: none;
            text-align: left;
            padding: 15px 20px;
            border-radius: 12px;
            margin: 4px 15px;
            min-height: 50px;
            border-left: 3px solid transparent;
        }

        #navButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.25),
                stop:1 rgba(70, 130, 255, 0.15));
            border-left: 3px solid #4682ff;
            transform: translateX(3px);
        }

        #navButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.35),
                stop:1 rgba(70, 130, 255, 0.25));
            transform: translateX(1px);
        }

        /* Bouton actif */
        #activeNavButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.3),
                stop:1 rgba(70, 130, 255, 0.2));
            border-left: 3px solid #4682ff;
            border-radius: 12px;
        }

        /* ===== ICÔNES DE NAVIGATION ===== */
        #navIcon {
            font-size: 22px;
            color: #4682ff;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }

        #navButton:hover #navIcon {
            color: #6fa8ff;
            transform: scale(1.1);
        }

        #activeNavButton #navIcon {
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
        }

        /* ===== TITRES DE NAVIGATION AMÉLIORÉS ===== */
        #navTitle {
            font-size: 15px;
            color: #ffffff;
            font-weight: 600;
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.3px;
        }

        #navButton:hover #navTitle {
            color: #ffffff;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        #activeNavButton #navTitle {
            color: #ffffff;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
        }

        /* ===== ZONE UTILISATEUR AMÉLIORÉE ===== */
        #userInfo {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.15),
                stop:1 rgba(70, 130, 255, 0.08));
            border-top: 2px solid rgba(70, 130, 255, 0.3);
            border-radius: 15px;
            margin: 15px;
            padding: 15px;
            border: 2px solid rgba(70, 130, 255, 0.2);
        }

        #userName {
            font-size: 15px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }

        #userRole {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        #userActionButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.3),
                stop:1 rgba(70, 130, 255, 0.2));
            border: 2px solid rgba(70, 130, 255, 0.4);
            border-radius: 18px;
            font-size: 13px;
            color: #ffffff;
            font-weight: 600;
            padding: 8px 16px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }

        #userActionButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.5),
                stop:1 rgba(70, 130, 255, 0.3));
            border-color: #4682ff;
            transform: translateY(-1px);
        }

        #userActionButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.6),
                stop:1 rgba(70, 130, 255, 0.4));
            transform: translateY(0px);
        }

        /* ===== ZONE DE NAVIGATION AVEC SCROLL ===== */
        #navScroll {
            background: transparent;
            border: none;
        }

        #navScroll QScrollBar:vertical {
            background: rgba(255, 255, 255, 0.1);
            width: 10px;
            border-radius: 5px;
            margin: 2px;
        }

        #navScroll QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.7),
                stop:1 rgba(70, 130, 255, 0.5));
            border-radius: 5px;
            min-height: 25px;
            margin: 2px;
        }

        #navScroll QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #4682ff, stop:1 #6fa8ff);
        }

        #navScroll QScrollBar::add-line:vertical,
        #navScroll QScrollBar::sub-line:vertical {
            height: 0;
            background: transparent;
        }

        /* ===== ANIMATIONS ET TRANSITIONS ===== */
        #navButton, #navIcon, #navTitle, #userActionButton {
            transition: all 0.3s ease;
        }

        #navButton:hover {
            transition: all 0.2s ease;
        }

        /* ===== SÉPARATEURS OPTIONNELS ===== */
        .nav-separator {
            height: 1px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent,
                stop:0.5 rgba(70, 130, 255, 0.3),
                stop:1 transparent);
            margin: 10px 20px;
        }

        /* ===== BADGES DE NOTIFICATION ===== */
        .nav-badge {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ff6b6b, stop:1 #ee5a52);
            color: white;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            padding: 2px 6px;
            min-width: 16px;
            text-align: center;
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1200px) {
            #sidebar {
                min-width: 250px;
                max-width: 250px;
            }
            
            #navTitle {
                font-size: 14px;
            }
            
            #navIcon {
                font-size: 20px;
            }
        }

        /* ===== MODE COMPACT (OPTIONNEL) ===== */
        .sidebar-compact #navTitle {
            display: none;
        }
        
        .sidebar-compact #sidebar {
            min-width: 80px;
            max-width: 80px;
        }
        
        .sidebar-compact #navButton {
            padding: 15px 10px;
            text-align: center;
        }
        
        .sidebar-compact #navIcon {
            margin: 0 auto;
        }
    """

def get_dark_theme_navigation():
    """Retourne les styles pour le thème sombre"""
    return """
        /* ===== THÈME SOMBRE NAVIGATION ===== */
        .dark-theme #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(15, 20, 35, 0.98),
                stop:1 rgba(20, 25, 45, 0.98));
        }
        
        .dark-theme #navTitle {
            color: #ffffff;
        }
        
        .dark-theme #navIcon {
            color: #4682ff;
        }
        
        .dark-theme #navButton:hover #navTitle {
            color: #ffffff;
        }
        
        .dark-theme #navButton:hover #navIcon {
            color: #6fa8ff;
        }
    """

def get_light_theme_navigation():
    """Retourne les styles pour le thème clair"""
    return """
        /* ===== THÈME CLAIR NAVIGATION ===== */
        .light-theme #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(248, 250, 252, 0.98),
                stop:1 rgba(255, 255, 255, 0.98));
            border-right-color: rgba(70, 130, 255, 0.2);
        }
        
        .light-theme #navTitle {
            color: #2c3e50;
            text-shadow: none;
        }
        
        .light-theme #navIcon {
            color: #3498db;
            text-shadow: none;
        }
        
        .light-theme #navButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(52, 152, 219, 0.15),
                stop:1 rgba(52, 152, 219, 0.08));
        }
        
        .light-theme #navButton:hover #navTitle {
            color: #2c3e50;
        }
        
        .light-theme #navButton:hover #navIcon {
            color: #2980b9;
        }
        
        .light-theme #userName {
            color: #2c3e50;
            text-shadow: none;
        }
        
        .light-theme #userActionButton {
            color: #2c3e50;
            text-shadow: none;
        }
    """

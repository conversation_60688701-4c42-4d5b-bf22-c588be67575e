#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que la correction du Product.is_active fonctionne
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_product_is_active():
    """Test de la propriété is_active du modèle Product"""
    try:
        from src.dal.models.product import Product, ProductStatus
        
        # Créer un produit de test
        product = Product(
            code="TEST001",
            name="Produit Test",
            status=ProductStatus.ACTIVE
        )
        
        # Tester la propriété is_active
        assert product.is_active == True, "Le produit actif devrait retourner True"
        print("✅ Test 1 réussi: Produit actif")
        
        # Changer le statut
        product.status = ProductStatus.INACTIVE
        assert product.is_active == False, "Le produit inactif devrait retourner False"
        print("✅ Test 2 réussi: Produit inactif")
        
        # Tester avec statut discontinué
        product.status = ProductStatus.DISCONTINUED
        assert product.is_active == False, "Le produit discontinué devrait retourner False"
        print("✅ Test 3 réussi: Produit discontinué")
        
        print("\n🎉 Tous les tests de Product.is_active ont réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def test_database_query():
    """Test des requêtes de base de données avec is_active"""
    try:
        from src.dal.models.product import Product, ProductStatus
        from src.dal.database import db_manager
        
        # Test de requête avec is_active (propriété)
        with db_manager.get_session() as session:
            # Cette requête devrait maintenant fonctionner
            active_products = session.query(Product).filter(Product.status == ProductStatus.ACTIVE).all()
            print(f"✅ Requête avec status réussie: {len(active_products)} produits actifs trouvés")
            
            # Test avec la propriété is_active (ne fonctionne que sur les objets chargés)
            all_products = session.query(Product).all()
            active_count = sum(1 for p in all_products if p.is_active)
            print(f"✅ Propriété is_active fonctionne: {active_count} produits actifs via propriété")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de base de données: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test de la correction Product.is_active\n")
    
    success1 = test_product_is_active()
    success2 = test_database_query()
    
    if success1 and success2:
        print("\n🎉 Tous les tests ont réussi! La correction fonctionne correctement.")
    else:
        print("\n❌ Certains tests ont échoué.")

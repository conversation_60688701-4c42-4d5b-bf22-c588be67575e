/* ========================================
   STYLES COMPLETS MODULE COMMERCIAL GSCOM
   Toutes les améliorations demandées
   ======================================== */

/* ===== FOND UNIFORME ET ÉLÉGANT ===== */
QWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(28, 35, 52, 0.98),
        stop:1 rgba(35, 42, 65, 0.98));
    color: #ffffff;
    font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
}

/* ===== TITRE MODULE - CONTRASTE AMÉLIORÉ ===== */
#moduleTitle {
    color: #ffffff;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
    margin: 10px 0;
    padding: 15px 0;
}

/* ===== BOUTON PRINCIPAL ÉLÉGANT ===== */
#primaryButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #4a90e2,
        stop:0.5 #357abd,
        stop:1 #2c5aa0);
    border: 2px solid #1e3a8a;
    border-radius: 12px;
    color: #ffffff;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 15px;
    padding: 14px 28px;
    min-width: 180px;
    min-height: 45px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.4);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

#primaryButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5ba3f5,
        stop:0.5 #4a90e2,
        stop:1 #357abd);
    border-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);
}

#primaryButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #357abd,
        stop:1 #2c5aa0);
    transform: translateY(0px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* ===== ONGLETS MODERNES ET COHÉRENTS ===== */
#commercialTabs::pane {
    border: 2px solid rgba(74, 144, 226, 0.4);
    border-radius: 16px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.12),
        stop:1 rgba(255, 255, 255, 0.06));
    margin-top: 12px;
    padding: 20px;
}

#commercialTabs::tab-bar {
    alignment: center;
}

#commercialTabs QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.18),
        stop:1 rgba(255, 255, 255, 0.10));
    border: 2px solid rgba(74, 144, 226, 0.3);
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 14px 24px;
    margin-right: 4px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 14px;
    min-width: 120px;
}

#commercialTabs QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.4),
        stop:1 rgba(74, 144, 226, 0.25));
    border-color: #4a90e2;
    color: #ffffff;
    font-weight: bold;
}

#commercialTabs QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.3),
        stop:1 rgba(74, 144, 226, 0.15));
    border-color: rgba(74, 144, 226, 0.5);
}

/* ===== BARRE D'OUTILS MODERNE ===== */
QFrame[objectName="toolbarFrame"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.12),
        stop:1 rgba(255, 255, 255, 0.06));
    border: 2px solid rgba(74, 144, 226, 0.25);
    border-radius: 12px;
    padding: 15px 20px;
    margin: 10px 0;
}

/* ===== BOUTONS SECONDAIRES ===== */
QPushButton[objectName="secondaryButton"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.18),
        stop:1 rgba(255, 255, 255, 0.10));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    color: #ffffff;
    font-family: 'Segoe UI', sans-serif;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 20px;
    min-width: 140px;
    min-height: 38px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

QPushButton[objectName="secondaryButton"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.3),
        stop:1 rgba(74, 144, 226, 0.2));
    border-color: rgba(74, 144, 226, 0.6);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(74, 144, 226, 0.2);
}

/* ===== BARRE DE RECHERCHE AMÉLIORÉE ===== */
QLineEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.95),
        stop:1 rgba(248, 250, 252, 0.95));
    border: 2px solid rgba(74, 144, 226, 0.3);
    border-radius: 10px;
    padding: 12px 20px 12px 45px;
    color: #2c3e50;
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
    font-weight: 500;
    min-height: 20px;
    min-width: 280px;
}

QLineEdit:focus {
    border: 3px solid #4a90e2;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 1.0),
        stop:1 rgba(250, 252, 255, 1.0));
    box-shadow: 0 0 12px rgba(74, 144, 226, 0.3);
}

QLineEdit::placeholder {
    color: rgba(100, 116, 139, 0.7);
    font-style: italic;
}

/* ===== TABLEAUX AVEC ESPACEMENT AMÉLIORÉ ===== */
QTableWidget {
    background: rgba(255, 255, 255, 0.97);
    border: 2px solid rgba(74, 144, 226, 0.3);
    border-radius: 12px;
    gridline-color: rgba(74, 144, 226, 0.15);
    color: #2c3e50;
    font-family: 'Segoe UI', sans-serif;
    font-size: 13px;
    selection-background-color: rgba(74, 144, 226, 0.2);
    alternate-background-color: rgba(248, 250, 252, 0.5);
}

QTableWidget::item {
    background: transparent;
    border: none;
    padding: 14px 12px;
    color: #374151;
    border-bottom: 1px solid rgba(74, 144, 226, 0.1);
}

QTableWidget::item:selected {
    background: rgba(74, 144, 226, 0.2);
    color: #1f2937;
}

QTableWidget::item:hover {
    background: rgba(74, 144, 226, 0.1);
}

/* ===== EN-TÊTES DE TABLEAU MODERNES ===== */
QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.5),
        stop:1 rgba(74, 144, 226, 0.35));
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 0;
    padding: 16px 12px;
    color: #ffffff;
    font-family: 'Segoe UI', sans-serif;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.7),
        stop:1 rgba(74, 144, 226, 0.5));
}

QHeaderView::section:first {
    border-top-left-radius: 10px;
}

QHeaderView::section:last {
    border-top-right-radius: 10px;
}

/* ===== BADGES DE STATUT COLORÉS ===== */
QLabel[objectName="statusBadge"] {
    border-radius: 8px;
    padding: 6px 12px;
    font-weight: bold;
    font-size: 12px;
    text-align: center;
    min-width: 80px;
}

QLabel[objectName="statusBadge"][status="En attente"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #fbbf24, stop:1 #f59e0b);
    color: #ffffff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

QLabel[objectName="statusBadge"][status="Accepté"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #10b981, stop:1 #059669);
    color: #ffffff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

QLabel[objectName="statusBadge"][status="Refusé"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #ef4444, stop:1 #dc2626);
    color: #ffffff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

/* ===== BOUTONS D'ACTION UNIFORMISÉS ===== */
QPushButton[objectName="actionButton"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.3),
        stop:1 rgba(74, 144, 226, 0.2));
    border: 2px solid rgba(74, 144, 226, 0.4);
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
    font-family: 'Segoe UI', sans-serif;
    font-weight: 600;
    font-size: 12px;
    min-width: 32px;
    min-height: 32px;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

QPushButton[objectName="actionButton"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.5),
        stop:1 rgba(74, 144, 226, 0.35));
    border-color: #4a90e2;
    transform: scale(1.05);
}

QPushButton[objectName="actionButton"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(74, 144, 226, 0.6),
        stop:1 rgba(74, 144, 226, 0.45));
    transform: scale(0.98);
}

/* ===== CARTES DE STATISTIQUES HARMONIEUSES ===== */
#statsFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.08),
        stop:1 rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(74, 144, 226, 0.25);
    border-radius: 16px;
    margin: 20px 0;
    padding: 20px;
}

#statCard {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.15),
        stop:1 rgba(255, 255, 255, 0.08));
    border: 2px solid rgba(74, 144, 226, 0.3);
    border-radius: 12px;
    margin: 5px;
    padding: 20px 15px;
    min-height: 90px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#statCard:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.2),
        stop:1 rgba(255, 255, 255, 0.12));
    border-color: rgba(74, 144, 226, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

#statValue {
    font-family: 'Segoe UI', sans-serif;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin-bottom: 8px;
}

#statTitle {
    color: rgba(255, 255, 255, 0.9);
    font-family: 'Segoe UI', sans-serif;
    font-size: 13px;
    font-weight: 600;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

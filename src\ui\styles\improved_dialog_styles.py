#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Styles améliorés pour les dialogues modernes GSCOM
Correction de tous les problèmes de lisibilité et d'interface
"""

def get_improved_dialog_styles():
    """Retourne les styles CSS améliorés pour les dialogues"""
    return """
        /* ========================================
           STYLES AMÉLIORÉS POUR DIALOGUES GSCOM
           ======================================== */

        /* Frame principal - Fond plus contrasté */
        #mainFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(25, 30, 45, 0.98),
                stop:1 rgba(35, 40, 65, 0.98));
            border: 2px solid rgba(70, 130, 255, 0.6);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* En-tête amélioré */
        #headerFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.25),
                stop:1 rgba(100, 150, 255, 0.25));
            border-radius: 14px 14px 0 0;
            border-bottom: 2px solid rgba(70, 130, 255, 0.3);
            padding: 15px 20px;
        }

        /* Titres plus lisibles */
        #titleLabel {
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        #subtitleLabel {
            color: rgba(255, 255, 255, 0.85);
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            margin-top: 5px;
        }

        /* Boutons de contrôle améliorés */
        #controlButton {
            background: rgba(70, 130, 255, 0.2);
            border: 2px solid rgba(70, 130, 255, 0.4);
            border-radius: 18px;
            color: #ffffff;
            font-weight: bold;
            font-size: 14px;
            min-width: 36px;
            min-height: 36px;
        }

        #controlButton:hover {
            background: rgba(70, 130, 255, 0.4);
            border-color: #4682ff;
            transform: scale(1.05);
        }

        #closeButton {
            background: rgba(255, 80, 80, 0.25);
            border: 2px solid rgba(255, 80, 80, 0.4);
            border-radius: 18px;
            color: #ffffff;
            font-weight: bold;
            font-size: 14px;
            min-width: 36px;
            min-height: 36px;
        }

        #closeButton:hover {
            background: rgba(255, 80, 80, 0.5);
            border-color: #ff5050;
            transform: scale(1.05);
        }

        /* Zone de contenu */
        #contentScrollArea {
            background: transparent;
            border: none;
            margin: 10px;
        }

        #contentWidget {
            background: transparent;
            padding: 10px;
        }

        /* Groupes de formulaire - Sections plus distinctes */
        QGroupBox {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.12),
                stop:1 rgba(255, 255, 255, 0.08));
            border: 2px solid rgba(70, 130, 255, 0.4);
            border-radius: 12px;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 15px;
            color: #4682ff;
            margin-top: 20px;
            padding-top: 25px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 20px;
            top: -10px;
            padding: 8px 16px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.8),
                stop:1 rgba(100, 150, 255, 0.8));
            border: 2px solid rgba(70, 130, 255, 0.6);
            border-radius: 10px;
            color: #ffffff;
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        /* Labels de champs - Plus lisibles */
        #fieldLabel {
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            min-width: 140px;
            padding-right: 10px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }

        /* Champs de saisie - Contraste amélioré */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.9),
                stop:1 rgba(245, 245, 245, 0.9));
            border: 2px solid rgba(70, 130, 255, 0.3);
            border-radius: 8px;
            padding: 12px 15px;
            color: #2c3e50;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            min-height: 20px;
            selection-background-color: rgba(70, 130, 255, 0.3);
        }

        /* Focus state pour les champs */
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus,
        QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
            border: 3px solid #4682ff;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1.0),
                stop:1 rgba(250, 250, 250, 1.0));
            box-shadow: 0 0 10px rgba(70, 130, 255, 0.3);
        }

        /* Placeholder text */
        QLineEdit::placeholder, QTextEdit::placeholder {
            color: rgba(100, 100, 100, 0.7);
            font-style: italic;
        }

        /* ComboBox amélioré */
        QComboBox::drop-down {
            border: none;
            width: 35px;
            background: rgba(70, 130, 255, 0.2);
            border-radius: 0 6px 6px 0;
        }

        QComboBox::down-arrow {
            image: none;
            border: 3px solid #4682ff;
            width: 8px;
            height: 8px;
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
            margin-right: 10px;
        }

        QComboBox QAbstractItemView {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #4682ff;
            border-radius: 8px;
            color: #2c3e50;
            selection-background-color: rgba(70, 130, 255, 0.3);
            padding: 5px;
        }

        /* SpinBox amélioré */
        QSpinBox::up-button, QDoubleSpinBox::up-button {
            background: rgba(70, 130, 255, 0.3);
            border: 1px solid rgba(70, 130, 255, 0.5);
            border-radius: 4px;
            width: 25px;
            margin: 2px;
        }

        QSpinBox::down-button, QDoubleSpinBox::down-button {
            background: rgba(70, 130, 255, 0.3);
            border: 1px solid rgba(70, 130, 255, 0.5);
            border-radius: 4px;
            width: 25px;
            margin: 2px;
        }

        QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
        QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
            background: rgba(70, 130, 255, 0.5);
        }

        /* CheckBox amélioré */
        QCheckBox {
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            spacing: 10px;
        }

        QCheckBox::indicator {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(70, 130, 255, 0.6);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.9);
        }

        QCheckBox::indicator:checked {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #4682ff, stop:1 #6fa8ff);
            border-color: #4682ff;
        }

        QCheckBox::indicator:hover {
            border-color: #4682ff;
            background: rgba(255, 255, 255, 1.0);
        }
    """

def get_improved_table_styles():
    """Retourne les styles pour les tableaux"""
    return """
        /* Tableaux améliorés */
        QTableWidget {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(70, 130, 255, 0.4);
            border-radius: 10px;
            gridline-color: rgba(70, 130, 255, 0.2);
            color: #2c3e50;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 13px;
            selection-background-color: rgba(70, 130, 255, 0.3);
        }

        QTableWidget::item {
            background: transparent;
            border: none;
            padding: 10px 8px;
            color: #2c3e50;
        }

        QTableWidget::item:selected {
            background: rgba(70, 130, 255, 0.3);
            color: #1a202c;
        }

        QTableWidget::item:hover {
            background: rgba(70, 130, 255, 0.1);
        }

        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.4),
                stop:1 rgba(70, 130, 255, 0.3));
            border: 1px solid rgba(70, 130, 255, 0.3);
            padding: 12px 8px;
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 13px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }

        QHeaderView::section:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.6),
                stop:1 rgba(70, 130, 255, 0.5));
        }

        /* Barres de défilement améliorées */
        QScrollBar:vertical {
            background: rgba(255, 255, 255, 0.1);
            width: 14px;
            border-radius: 7px;
            margin: 0;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.7),
                stop:1 rgba(70, 130, 255, 0.5));
            border-radius: 7px;
            min-height: 25px;
            margin: 2px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.9),
                stop:1 rgba(70, 130, 255, 0.7));
        }

        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            height: 0;
            background: transparent;
        }

        QScrollBar:horizontal {
            background: rgba(255, 255, 255, 0.1);
            height: 14px;
            border-radius: 7px;
            margin: 0;
        }

        QScrollBar::handle:horizontal {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.7),
                stop:1 rgba(70, 130, 255, 0.5));
            border-radius: 7px;
            min-width: 25px;
            margin: 2px;
        }
    """

def get_improved_footer_styles():
    """Retourne les styles pour le pied de page"""
    return """
        /* Pied de page amélioré */
        #footerFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.08),
                stop:1 rgba(255, 255, 255, 0.04));
            border-radius: 0 0 14px 14px;
            border-top: 2px solid rgba(70, 130, 255, 0.3);
            padding: 20px;
        }

        /* Bouton Annuler - Gris clair professionnel */
        #cancelButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(180, 180, 180, 0.9),
                stop:1 rgba(160, 160, 160, 0.9));
            border: 2px solid rgba(140, 140, 140, 0.8);
            border-radius: 10px;
            color: #2c3e50;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 25px;
            min-width: 120px;
            min-height: 40px;
        }

        #cancelButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(200, 200, 200, 0.9),
                stop:1 rgba(180, 180, 180, 0.9));
            border-color: rgba(120, 120, 120, 0.9);
            transform: translateY(-2px);
        }

        #cancelButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(160, 160, 160, 0.9),
                stop:1 rgba(140, 140, 140, 0.9));
            transform: translateY(0px);
        }

        /* Bouton Enregistrer - Bleu vif principal */
        #acceptButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4682ff,
                stop:1 #2c5aa0);
            border: 2px solid #1e3a8a;
            border-radius: 10px;
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 14px;
            padding: 12px 25px;
            min-width: 120px;
            min-height: 40px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        #acceptButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a9cff,
                stop:1 #3d6bb5);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(70, 130, 255, 0.4);
        }

        #acceptButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3366cc,
                stop:1 #1e3a8a);
            transform: translateY(0px);
        }

        /* Boutons d'action dans les dialogues */
        QPushButton[objectName="actionButton"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.4),
                stop:1 rgba(70, 130, 255, 0.3));
            border: 2px solid rgba(70, 130, 255, 0.6);
            border-radius: 8px;
            padding: 10px 18px;
            color: #ffffff;
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 13px;
            min-width: 110px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }

        QPushButton[objectName="actionButton"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(70, 130, 255, 0.6),
                stop:1 rgba(70, 130, 255, 0.5));
            border-color: #4682ff;
            transform: translateY(-1px);
        }
    """

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GSCOM - Interface Accessible</title>
    <style>
        /* ===== VARIABLES CSS POUR ACCESSIBILITÉ ===== */
        :root {
            /* Mode Clair (par défaut) - Contrastes optimaux */
            --bg-primary-light: #ffffff;
            --bg-secondary-light: #f8fafc;
            --bg-sidebar-light: #f1f5f9;
            --text-primary-light: #1e293b;
            --text-secondary-light: #475569;
            --accent-primary-light: #2563eb;
            --border-light: #e2e8f0;
            --hover-light: #f1f5f9;
            
            /* Mode Sombre */
            --bg-primary-dark: #0f172a;
            --bg-secondary-dark: #1e293b;
            --bg-sidebar-dark: #334155;
            --text-primary-dark: #f8fafc;
            --text-secondary-dark: #e2e8f0;
            --accent-primary-dark: #60a5fa;
            --border-dark: #475569;
            --hover-dark: #475569;
            
            /* Tailles pour accessibilité */
            --font-size-base: 16px;
            --font-size-large: 18px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --border-radius: 8px;
        }

        /* ===== MODE CLAIR (DÉFAUT) ===== */
        body {
            background: var(--bg-primary-light);
            color: var(--text-primary-light);
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: var(--font-size-base);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* ===== SIDEBAR ACCESSIBLE ===== */
        .sidebar {
            background: var(--bg-sidebar-light);
            border-right: 2px solid var(--border-light);
            width: 280px;
            padding: var(--spacing-lg);
        }

        .logo {
            font-size: 48px;
            color: var(--accent-primary-light);
            text-align: center;
            margin-bottom: var(--spacing-md);
        }

        .title {
            color: var(--text-primary-light);
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        .nav-button {
            background: transparent;
            border: 2px solid transparent;
            border-radius: var(--border-radius);
            color: var(--text-primary-light);
            padding: var(--spacing-md);
            margin: 4px 0;
            width: 100%;
            text-align: left;
            cursor: pointer;
            display: flex;
            align-items: center;
            min-height: 60px;
            font-size: var(--font-size-base);
        }

        .nav-button:hover {
            background: var(--hover-light);
            border-color: var(--accent-primary-light);
            color: var(--accent-primary-light);
        }

        .nav-icon {
            color: var(--accent-primary-light);
            font-size: 20px;
            font-weight: bold;
            margin-right: var(--spacing-md);
            min-width: 32px;
        }

        .nav-title {
            color: var(--text-primary-light);
            font-weight: 600;
        }

        .nav-button:hover .nav-title {
            color: var(--accent-primary-light);
            font-weight: bold;
        }

        /* ===== ZONE DE CONTENU ===== */
        .content {
            flex: 1;
            padding: var(--spacing-lg);
            background: var(--bg-primary-light);
        }

        .dashboard-title {
            color: var(--accent-primary-light);
            font-size: 32px;
            font-weight: bold;
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .kpi-card {
            background: var(--bg-secondary-light);
            border: 2px solid var(--border-light);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            min-height: 120px;
        }

        .kpi-title {
            color: var(--text-secondary-light);
            font-size: var(--font-size-base);
            font-weight: 600;
            margin-bottom: 8px;
        }

        .kpi-value {
            color: var(--text-primary-light);
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .kpi-icon {
            color: var(--accent-primary-light);
            font-size: 24px;
            float: right;
        }

        /* ===== BOUTONS ACCESSIBLES ===== */
        .btn {
            background: var(--accent-primary-light);
            border: 2px solid var(--accent-primary-light);
            border-radius: var(--border-radius);
            color: white;
            font-size: var(--font-size-base);
            font-weight: 600;
            padding: 12px 24px;
            cursor: pointer;
            min-height: 40px;
            margin: 4px;
        }

        .btn:hover {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-primary-light);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 20px;
            cursor: pointer;
        }

        /* ===== MODE SOMBRE ===== */
        .dark-theme {
            --bg-primary-light: var(--bg-primary-dark);
            --bg-secondary-light: var(--bg-secondary-dark);
            --bg-sidebar-light: var(--bg-sidebar-dark);
            --text-primary-light: var(--text-primary-dark);
            --text-secondary-light: var(--text-secondary-dark);
            --accent-primary-light: var(--accent-primary-dark);
            --border-light: var(--border-dark);
            --hover-light: var(--hover-dark);
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                padding: var(--spacing-md);
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Accessible -->
        <div class="sidebar">
            <div class="logo">🏢</div>
            <div class="title">GSCOM - Gestion Commerciale</div>
            
            <button class="nav-button">
                <span class="nav-icon">📊</span>
                <span class="nav-title">Dashboard</span>
            </button>
            
            <button class="nav-button">
                <span class="nav-icon">👥</span>
                <span class="nav-title">Clients</span>
            </button>
            
            <button class="nav-button">
                <span class="nav-icon">🏭</span>
                <span class="nav-title">Fournisseurs</span>
            </button>
            
            <button class="nav-button">
                <span class="nav-icon">📦</span>
                <span class="nav-title">Produits</span>
            </button>
            
            <button class="nav-button">
                <span class="nav-icon">🧾</span>
                <span class="nav-title">Factures</span>
            </button>
        </div>

        <!-- Zone de Contenu -->
        <div class="content">
            <h1 class="dashboard-title">Bienvenue, Administrateur !</h1>
            
            <!-- Cartes KPI Accessibles -->
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-icon">💰</div>
                    <div class="kpi-title">Chiffre d'Affaires</div>
                    <div class="kpi-value">125 450 DA</div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-icon">📋</div>
                    <div class="kpi-title">Commandes</div>
                    <div class="kpi-value">23</div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-icon">👥</div>
                    <div class="kpi-title">Clients</div>
                    <div class="kpi-value">156</div>
                </div>
                
                <div class="kpi-card">
                    <div class="kpi-icon">📦</div>
                    <div class="kpi-title">Produits</div>
                    <div class="kpi-value">89</div>
                </div>
            </div>

            <!-- Boutons d'Action -->
            <div style="text-align: center; margin-top: 32px;">
                <button class="btn">Nouvelle Commande</button>
                <button class="btn">Ajouter Client</button>
                <button class="btn">Gérer Stock</button>
            </div>
        </div>
    </div>

    <!-- Bouton de Basculement de Thème -->
    <button class="theme-toggle" onclick="toggleTheme()" id="themeBtn">☀️</button>

    <script>
        function toggleTheme() {
            const body = document.body;
            const themeBtn = document.getElementById('themeBtn');
            
            if (body.classList.contains('dark-theme')) {
                body.classList.remove('dark-theme');
                themeBtn.textContent = '☀️';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-theme');
                themeBtn.textContent = '🌙';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Charger le thème sauvegardé
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeBtn = document.getElementById('themeBtn');
            
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
                themeBtn.textContent = '🌙';
            } else {
                themeBtn.textContent = '☀️';
            }
        });
    </script>
</body>
</html>

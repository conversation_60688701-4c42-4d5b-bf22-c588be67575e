#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide de l'application GSCOM
Lance l'application pour vérification avant compilation
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def quick_test():
    """Test rapide de l'application"""
    print("🚀 Test rapide de GSCOM...")
    print("=" * 40)

    try:
        # Test des imports
        print("📦 Test des imports...")
        from PyQt5.QtWidgets import QApplication
        from src.ui.login_window import LoginWindow
        from src.dal.database import db_manager
        print("✅ Imports réussis")

        # Initialisation base de données
        print("🗄️ Initialisation base de données...")
        db_manager.create_tables()
        print("✅ Base de données prête")

        # Création utilisateur admin si nécessaire
        print("👤 Vérification utilisateur admin...")
        from src.bll.user_service import UserService

        user_service = UserService()
        admin_user = user_service.get_user_by_username("admin")

        if not admin_user:
            admin_data = {
                'username': 'admin',
                'password': 'admin123',
                'email': '<EMAIL>',
                'first_name': 'Administrateur',
                'last_name': 'GSCOM',
                'is_admin': True,
                'is_active': True
            }
            user_service.create_user(admin_data)
            print("✅ Utilisateur admin créé")
        else:
            print("✅ Utilisateur admin existe")

        # Lancement de l'application
        print("🖥️ Lancement de l'interface...")
        app = QApplication(sys.argv)
        app.setApplicationName("GSCOM")

        login_window = LoginWindow()
        login_window.show()

        print("✅ Application lancée avec succès!")
        print("\n🔑 Connexion par défaut:")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
        print("\n💡 Fermez la fenêtre pour arrêter le test")

        return app.exec_()

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(quick_test())

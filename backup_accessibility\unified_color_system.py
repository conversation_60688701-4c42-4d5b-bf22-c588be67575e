#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système unifié de couleurs pour GSCOM
Application cohérente des améliorations de couleurs à tous les composants
"""

def get_unified_color_palette():
    """Retourne la palette de couleurs unifiée"""
    return {
        # Couleurs principales
        'primary_blue': '#4682ff',
        'secondary_blue': '#6fa8ff',
        'dark_blue': '#2c5aa0',
        'accent_cyan': '#00d4ff',
        
        # Couleurs de fond
        'bg_primary': 'rgba(18, 24, 38, 0.98)',
        'bg_secondary': 'rgba(25, 35, 55, 0.98)',
        'bg_tertiary': 'rgba(20, 25, 40, 0.98)',
        
        # Couleurs de texte
        'text_primary': '#ffffff',
        'text_secondary': 'rgba(255, 255, 255, 0.8)',
        'text_tertiary': 'rgba(255, 255, 255, 0.6)',
        'text_on_light': '#2c3e50',
        
        # Couleurs d'état
        'success': '#27ae60',
        'warning': '#f39c12',
        'error': '#e74c3c',
        'info': '#3498db',
        
        # Couleurs de bordure
        'border_primary': 'rgba(70, 130, 255, 0.4)',
        'border_secondary': 'rgba(255, 255, 255, 0.15)',
        'border_light': 'rgba(255, 255, 255, 0.1)',
        
        # Couleurs de fond pour éléments
        'element_bg_light': 'rgba(255, 255, 255, 0.12)',
        'element_bg_medium': 'rgba(255, 255, 255, 0.08)',
        'element_bg_dark': 'rgba(255, 255, 255, 0.05)',
        
        # Couleurs hover
        'hover_primary': 'rgba(70, 130, 255, 0.25)',
        'hover_secondary': 'rgba(70, 130, 255, 0.15)',
        'hover_light': 'rgba(255, 255, 255, 0.1)',
    }

def get_unified_navigation_styles():
    """Styles unifiés pour la navigation"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== NAVIGATION UNIFIÉE ===== */
        #sidebar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['bg_tertiary']},
                stop:1 {colors['bg_secondary']});
            border-right: 2px solid {colors['border_primary']};
            min-width: 280px;
            max-width: 280px;
        }}

        #navButton {{
            background: transparent;
            border: none;
            text-align: left;
            padding: 15px 20px;
            border-radius: 12px;
            margin: 4px 15px;
            min-height: 50px;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }}

        #navButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-left: 3px solid {colors['primary_blue']};
            transform: translateX(3px);
        }}

        #navTitle {{
            font-size: 15px;
            color: {colors['text_primary']};
            font-weight: 600;
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            letter-spacing: 0.3px;
        }}

        #navButton:hover #navTitle {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}

        #navIcon {{
            font-size: 22px;
            color: {colors['primary_blue']};
            font-weight: bold;
        }}

        #navButton:hover #navIcon {{
            color: {colors['secondary_blue']};
            transform: scale(1.1);
        }}

        #activeNavButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-left: 3px solid {colors['primary_blue']};
        }}

        #activeNavButton #navTitle {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}

        #activeNavButton #navIcon {{
            color: {colors['secondary_blue']};
        }}
    """

def get_unified_dialog_styles():
    """Styles unifiés pour les dialogues"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== DIALOGUES UNIFIÉS ===== */
        QDialog, QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['bg_primary']},
                stop:1 {colors['bg_secondary']});
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
        }}

        #mainFrame {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['bg_primary']},
                stop:1 {colors['bg_secondary']});
            border: 2px solid {colors['border_primary']};
            border-radius: 16px;
        }}

        #headerFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-radius: 14px 14px 0 0;
            border-bottom: 2px solid {colors['border_primary']};
            padding: 15px 20px;
        }}

        #titleLabel {{
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 20px;
            font-weight: bold;
        }}

        #subtitleLabel {{
            color: {colors['text_secondary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            margin-top: 5px;
        }}

        /* ===== GROUPES DE FORMULAIRE ===== */
        QGroupBox {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_primary']};
            border-radius: 12px;
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 15px;
            color: {colors['primary_blue']};
            margin-top: 20px;
            padding-top: 25px;
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 20px;
            top: -10px;
            padding: 8px 16px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['secondary_blue']});
            border: 2px solid {colors['primary_blue']};
            border-radius: 10px;
            color: {colors['text_primary']};
            font-weight: bold;
            font-size: 14px;
        }}

        /* ===== LABELS DE CHAMPS ===== */
        #fieldLabel {{
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            min-width: 140px;
            padding-right: 10px;
        }}

        /* ===== CHAMPS DE SAISIE ===== */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.95),
                stop:1 rgba(248, 250, 252, 0.95));
            border: 2px solid {colors['border_primary']};
            border-radius: 8px;
            padding: 12px 15px;
            color: {colors['text_on_light']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            min-height: 20px;
            selection-background-color: {colors['hover_primary']};
        }}

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, 
        QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
            border: 3px solid {colors['primary_blue']};
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1.0),
                stop:1 rgba(250, 252, 255, 1.0));
        }}

        QLineEdit::placeholder, QTextEdit::placeholder {{
            color: rgba(100, 116, 139, 0.7);
            font-style: italic;
        }}
    """

def get_unified_button_styles():
    """Styles unifiés pour les boutons"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== BOUTONS UNIFIÉS ===== */
        
        /* Bouton principal */
        #primaryButton, #acceptButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['dark_blue']});
            border: 2px solid {colors['dark_blue']};
            border-radius: 10px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 14px;
            padding: 12px 25px;
            min-width: 120px;
            min-height: 40px;
        }}

        #primaryButton:hover, #acceptButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary_blue']},
                stop:1 {colors['primary_blue']});
            border-color: {colors['primary_blue']};
            transform: translateY(-1px);
        }}

        #primaryButton:pressed, #acceptButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['dark_blue']},
                stop:1 rgba(44, 90, 160, 1.0));
            transform: translateY(0px);
        }}

        /* Bouton secondaire */
        #secondaryButton, #cancelButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_secondary']};
            border-radius: 10px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 25px;
            min-width: 120px;
            min-height: 40px;
        }}

        #secondaryButton:hover, #cancelButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-color: {colors['primary_blue']};
            transform: translateY(-1px);
        }}

        /* Boutons d'action */
        #actionButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border: 2px solid {colors['border_primary']};
            border-radius: 8px;
            padding: 8px 12px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 12px;
            min-width: 32px;
            min-height: 32px;
        }}

        #actionButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['secondary_blue']});
            border-color: {colors['primary_blue']};
            transform: scale(1.05);
        }}

        #actionButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['dark_blue']},
                stop:1 {colors['primary_blue']});
            transform: scale(0.98);
        }}
    """

def get_unified_table_styles():
    """Styles unifiés pour les tableaux"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== TABLEAUX UNIFIÉS ===== */
        QTableWidget {{
            background: rgba(255, 255, 255, 0.97);
            border: 2px solid {colors['border_primary']};
            border-radius: 12px;
            gridline-color: {colors['border_light']};
            color: {colors['text_on_light']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 13px;
            selection-background-color: {colors['hover_primary']};
            alternate-background-color: rgba(248, 250, 252, 0.5);
        }}

        QTableWidget::item {{
            background: transparent;
            border: none;
            padding: 14px 12px;
            color: #374151;
            border-bottom: 1px solid {colors['border_light']};
        }}

        QTableWidget::item:selected {{
            background: {colors['hover_primary']};
            color: #1f2937;
        }}

        QTableWidget::item:hover {{
            background: {colors['hover_secondary']};
        }}

        /* En-têtes de tableau */
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['dark_blue']});
            border: 1px solid {colors['border_primary']};
            border-radius: 0;
            padding: 16px 12px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 14px;
        }}

        QHeaderView::section:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary_blue']},
                stop:1 {colors['primary_blue']});
        }}

        QHeaderView::section:first {{
            border-top-left-radius: 10px;
        }}

        QHeaderView::section:last {{
            border-top-right-radius: 10px;
        }}
    """

def get_complete_unified_styles():
    """Retourne tous les styles unifiés combinés"""
    return (
        get_unified_navigation_styles() +
        get_unified_dialog_styles() +
        get_unified_button_styles() +
        get_unified_table_styles()
    )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fonctionnalités avancées du système de couleurs GSCOM
Extensions et optimisations pour une expérience utilisateur exceptionnelle
"""

import logging
import json
import os
from datetime import datetime, time
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QSettings
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QColor

from .unified_color_system import get_unified_color_palette
from .theme_manager import get_theme_manager

class AdvancedColorManager(QObject):
    """Gestionnaire avancé des couleurs avec fonctionnalités étendues"""
    
    color_scheme_changed = pyqtSignal(str)
    accessibility_mode_changed = pyqtSignal(bool)
    animation_speed_changed = pyqtSignal(float)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings = QSettings("GSCOM", "ColorSettings")
        
        # Paramètres avancés
        self.accessibility_mode = self.settings.value("accessibility_mode", False, bool)
        self.animation_speed = self.settings.value("animation_speed", 1.0, float)
        self.auto_theme_enabled = self.settings.value("auto_theme", True, bool)
        self.high_contrast_mode = self.settings.value("high_contrast", False, bool)
        self.color_blind_mode = self.settings.value("color_blind_mode", "none", str)
        
        # Timer pour le thème automatique
        self.auto_theme_timer = QTimer()
        self.auto_theme_timer.timeout.connect(self.check_auto_theme)
        if self.auto_theme_enabled:
            self.auto_theme_timer.start(60000)  # Vérifier chaque minute
        
        # Palettes personnalisées
        self.custom_palettes = self.load_custom_palettes()
        
    def enable_accessibility_mode(self, enabled=True):
        """Active/désactive le mode accessibilité renforcée"""
        self.accessibility_mode = enabled
        self.settings.setValue("accessibility_mode", enabled)
        
        if enabled:
            self.apply_accessibility_enhancements()
        else:
            self.restore_normal_mode()
        
        self.accessibility_mode_changed.emit(enabled)
        self.logger.info(f"Mode accessibilité: {'activé' if enabled else 'désactivé'}")
    
    def apply_accessibility_enhancements(self):
        """Applique les améliorations d'accessibilité"""
        enhanced_styles = self.get_accessibility_styles()
        app = QApplication.instance()
        if app:
            current_styles = app.styleSheet()
            app.setStyleSheet(current_styles + enhanced_styles)
    
    def get_accessibility_styles(self):
        """Retourne les styles d'accessibilité renforcée"""
        return """
            /* ===== MODE ACCESSIBILITÉ RENFORCÉE ===== */
            
            /* Contrastes maximaux */
            #navTitle {
                color: #ffffff !important;
                font-weight: bold !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
            }
            
            #navIcon {
                color: #00ff00 !important;  /* Vert vif pour visibilité maximale */
                font-size: 26px !important;
                font-weight: bold !important;
            }
            
            /* Bordures renforcées */
            #navButton {
                border: 3px solid #ffffff !important;
                margin: 6px 15px !important;
            }
            
            #navButton:hover {
                border: 4px solid #00ff00 !important;
                background: rgba(0, 255, 0, 0.3) !important;
            }
            
            /* Texte agrandi */
            #cardTitle {
                font-size: 18px !important;
                font-weight: bold !important;
            }
            
            #cardValue {
                font-size: 42px !important;
                font-weight: bold !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
            }
            
            /* Boutons plus visibles */
            #primaryButton, #acceptButton {
                border: 4px solid #ffffff !important;
                font-size: 16px !important;
                font-weight: bold !important;
                min-height: 50px !important;
            }
            
            /* Focus renforcé */
            QLineEdit:focus, QComboBox:focus {
                border: 5px solid #00ff00 !important;
                background: #ffffff !important;
            }
        """
    
    def set_animation_speed(self, speed):
        """Définit la vitesse des animations (0.1 à 3.0)"""
        speed = max(0.1, min(3.0, speed))
        self.animation_speed = speed
        self.settings.setValue("animation_speed", speed)
        
        # Appliquer la nouvelle vitesse
        self.apply_animation_speed()
        self.animation_speed_changed.emit(speed)
        self.logger.info(f"Vitesse d'animation définie: {speed}x")
    
    def apply_animation_speed(self):
        """Applique la vitesse d'animation aux styles"""
        duration = int(300 / self.animation_speed)  # Durée de base 300ms
        
        speed_styles = f"""
            /* ===== VITESSE D'ANIMATION PERSONNALISÉE ===== */
            #navButton {{
                transition: all {duration}ms ease !important;
            }}
            
            #navButton:hover {{
                transition: all {int(duration * 0.7)}ms ease !important;
            }}
            
            #primaryButton, #secondaryButton, #actionButton {{
                transition: all {duration}ms ease !important;
            }}
            
            #dashboardCard {{
                transition: all {int(duration * 1.2)}ms ease !important;
            }}
        """
        
        app = QApplication.instance()
        if app:
            current_styles = app.styleSheet()
            # Remplacer les styles d'animation existants
            app.setStyleSheet(current_styles + speed_styles)
    
    def enable_high_contrast_mode(self, enabled=True):
        """Active/désactive le mode contraste élevé"""
        self.high_contrast_mode = enabled
        self.settings.setValue("high_contrast", enabled)
        
        if enabled:
            self.apply_high_contrast_styles()
        else:
            self.restore_normal_contrast()
        
        self.logger.info(f"Mode contraste élevé: {'activé' if enabled else 'désactivé'}")
    
    def apply_high_contrast_styles(self):
        """Applique les styles de contraste élevé"""
        high_contrast_styles = """
            /* ===== MODE CONTRASTE ÉLEVÉ ===== */
            
            QWidget {
                background: #000000 !important;
                color: #ffffff !important;
            }
            
            #sidebar {
                background: #000000 !important;
                border-right: 3px solid #ffffff !important;
            }
            
            #navTitle {
                color: #ffffff !important;
                font-weight: bold !important;
            }
            
            #navIcon {
                color: #ffff00 !important;  /* Jaune vif */
            }
            
            #navButton:hover {
                background: #333333 !important;
                border: 3px solid #ffff00 !important;
            }
            
            #primaryButton {
                background: #ffffff !important;
                color: #000000 !important;
                border: 3px solid #ffffff !important;
            }
            
            #secondaryButton {
                background: #000000 !important;
                color: #ffffff !important;
                border: 3px solid #ffffff !important;
            }
            
            QLineEdit, QComboBox {
                background: #ffffff !important;
                color: #000000 !important;
                border: 3px solid #000000 !important;
            }
            
            QTableWidget {
                background: #ffffff !important;
                color: #000000 !important;
            }
            
            QHeaderView::section {
                background: #000000 !important;
                color: #ffffff !important;
                border: 2px solid #ffffff !important;
            }
        """
        
        app = QApplication.instance()
        if app:
            app.setStyleSheet(high_contrast_styles)
    
    def set_color_blind_mode(self, mode):
        """Définit le mode daltonien (none, protanopia, deuteranopia, tritanopia)"""
        valid_modes = ["none", "protanopia", "deuteranopia", "tritanopia"]
        if mode not in valid_modes:
            self.logger.warning(f"Mode daltonien invalide: {mode}")
            return
        
        self.color_blind_mode = mode
        self.settings.setValue("color_blind_mode", mode)
        
        if mode != "none":
            self.apply_color_blind_palette(mode)
        else:
            self.restore_normal_colors()
        
        self.logger.info(f"Mode daltonien défini: {mode}")
    
    def apply_color_blind_palette(self, mode):
        """Applique une palette adaptée aux daltoniens"""
        palettes = {
            "protanopia": {  # Déficience rouge
                "primary": "#0066cc",    # Bleu
                "secondary": "#ff9900",  # Orange
                "success": "#0099cc",    # Cyan
                "warning": "#ffcc00",    # Jaune
                "error": "#666666",      # Gris foncé
            },
            "deuteranopia": {  # Déficience verte
                "primary": "#0066ff",    # Bleu vif
                "secondary": "#ff6600",  # Orange vif
                "success": "#0099ff",    # Bleu clair
                "warning": "#ffcc00",    # Jaune
                "error": "#990099",      # Magenta
            },
            "tritanopia": {  # Déficience bleue
                "primary": "#cc0066",    # Rose
                "secondary": "#ff3300",  # Rouge
                "success": "#00cc66",    # Vert
                "warning": "#ffcc00",    # Jaune
                "error": "#cc0000",      # Rouge foncé
            }
        }
        
        palette = palettes.get(mode, {})
        if palette:
            color_blind_styles = f"""
                /* ===== PALETTE DALTONIEN: {mode.upper()} ===== */
                
                #navIcon {{
                    color: {palette['primary']} !important;
                }}
                
                #primaryButton {{
                    background: {palette['primary']} !important;
                }}
                
                #secondaryButton {{
                    background: {palette['secondary']} !important;
                }}
                
                #revenueCard {{
                    border-left-color: {palette['success']} !important;
                }}
                
                #ordersCard {{
                    border-left-color: {palette['primary']} !important;
                }}
                
                #clientsCard {{
                    border-left-color: {palette['secondary']} !important;
                }}
                
                #productsCard {{
                    border-left-color: {palette['error']} !important;
                }}
                
                #invoicesCard {{
                    border-left-color: {palette['warning']} !important;
                }}
            """
            
            app = QApplication.instance()
            if app:
                current_styles = app.styleSheet()
                app.setStyleSheet(current_styles + color_blind_styles)
    
    def enable_auto_theme(self, enabled=True):
        """Active/désactive le thème automatique selon l'heure"""
        self.auto_theme_enabled = enabled
        self.settings.setValue("auto_theme", enabled)
        
        if enabled:
            self.auto_theme_timer.start(60000)  # Vérifier chaque minute
            self.check_auto_theme()  # Vérification immédiate
        else:
            self.auto_theme_timer.stop()
        
        self.logger.info(f"Thème automatique: {'activé' if enabled else 'désactivé'}")
    
    def check_auto_theme(self):
        """Vérifie et applique le thème automatique selon l'heure"""
        if not self.auto_theme_enabled:
            return
        
        current_time = datetime.now().time()
        
        # Thème sombre de 18h à 8h, clair de 8h à 18h
        if time(18, 0) <= current_time or current_time < time(8, 0):
            target_theme = "dark"
        else:
            target_theme = "light"
        
        theme_manager = get_theme_manager()
        current_theme = theme_manager.get_current_theme()
        
        if current_theme != target_theme:
            theme_manager.set_theme(target_theme)
            self.logger.info(f"Thème automatique appliqué: {target_theme}")
    
    def create_custom_palette(self, name, colors):
        """Crée une palette personnalisée"""
        if not isinstance(colors, dict):
            self.logger.error("Les couleurs doivent être un dictionnaire")
            return False
        
        self.custom_palettes[name] = colors
        self.save_custom_palettes()
        self.logger.info(f"Palette personnalisée créée: {name}")
        return True
    
    def apply_custom_palette(self, name):
        """Applique une palette personnalisée"""
        if name not in self.custom_palettes:
            self.logger.error(f"Palette personnalisée introuvable: {name}")
            return False
        
        palette = self.custom_palettes[name]
        custom_styles = self.generate_custom_styles(palette)
        
        app = QApplication.instance()
        if app:
            app.setStyleSheet(custom_styles)
        
        self.color_scheme_changed.emit(name)
        self.logger.info(f"Palette personnalisée appliquée: {name}")
        return True
    
    def generate_custom_styles(self, palette):
        """Génère les styles CSS à partir d'une palette personnalisée"""
        return f"""
            /* ===== PALETTE PERSONNALISÉE ===== */
            
            #navIcon {{
                color: {palette.get('primary', '#4682ff')} !important;
            }}
            
            #navTitle {{
                color: {palette.get('text', '#ffffff')} !important;
            }}
            
            #primaryButton {{
                background: {palette.get('primary', '#4682ff')} !important;
                color: {palette.get('text', '#ffffff')} !important;
            }}
            
            #secondaryButton {{
                background: {palette.get('secondary', '#6fa8ff')} !important;
                color: {palette.get('text', '#ffffff')} !important;
            }}
            
            #sidebar {{
                background: {palette.get('background', 'rgba(20, 25, 40, 0.98)')} !important;
                border-right-color: {palette.get('border', 'rgba(70, 130, 255, 0.3)')} !important;
            }}
        """
    
    def load_custom_palettes(self):
        """Charge les palettes personnalisées"""
        try:
            palettes_file = os.path.join(os.path.expanduser("~"), ".gscom_palettes.json")
            if os.path.exists(palettes_file):
                with open(palettes_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des palettes: {e}")
        
        return {}
    
    def save_custom_palettes(self):
        """Sauvegarde les palettes personnalisées"""
        try:
            palettes_file = os.path.join(os.path.expanduser("~"), ".gscom_palettes.json")
            with open(palettes_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_palettes, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde des palettes: {e}")
    
    def export_settings(self, file_path):
        """Exporte tous les paramètres de couleur"""
        settings_data = {
            "accessibility_mode": self.accessibility_mode,
            "animation_speed": self.animation_speed,
            "auto_theme_enabled": self.auto_theme_enabled,
            "high_contrast_mode": self.high_contrast_mode,
            "color_blind_mode": self.color_blind_mode,
            "custom_palettes": self.custom_palettes,
            "export_date": datetime.now().isoformat()
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Paramètres exportés vers: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de l'export: {e}")
            return False
    
    def import_settings(self, file_path):
        """Importe les paramètres de couleur"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            
            # Appliquer les paramètres
            if "accessibility_mode" in settings_data:
                self.enable_accessibility_mode(settings_data["accessibility_mode"])
            
            if "animation_speed" in settings_data:
                self.set_animation_speed(settings_data["animation_speed"])
            
            if "auto_theme_enabled" in settings_data:
                self.enable_auto_theme(settings_data["auto_theme_enabled"])
            
            if "high_contrast_mode" in settings_data:
                self.enable_high_contrast_mode(settings_data["high_contrast_mode"])
            
            if "color_blind_mode" in settings_data:
                self.set_color_blind_mode(settings_data["color_blind_mode"])
            
            if "custom_palettes" in settings_data:
                self.custom_palettes = settings_data["custom_palettes"]
                self.save_custom_palettes()
            
            self.logger.info(f"Paramètres importés depuis: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'import: {e}")
            return False
    
    def restore_normal_mode(self):
        """Restaure le mode normal (désactive tous les modes spéciaux)"""
        theme_manager = get_theme_manager()
        theme_manager.apply_global_theme()
        self.logger.info("Mode normal restauré")
    
    def restore_normal_contrast(self):
        """Restaure le contraste normal"""
        self.restore_normal_mode()
    
    def restore_normal_colors(self):
        """Restaure les couleurs normales"""
        self.restore_normal_mode()
    
    def get_current_settings(self):
        """Retourne les paramètres actuels"""
        return {
            "accessibility_mode": self.accessibility_mode,
            "animation_speed": self.animation_speed,
            "auto_theme_enabled": self.auto_theme_enabled,
            "high_contrast_mode": self.high_contrast_mode,
            "color_blind_mode": self.color_blind_mode,
            "custom_palettes_count": len(self.custom_palettes)
        }

# Instance globale du gestionnaire avancé
advanced_color_manager = AdvancedColorManager()

def get_advanced_color_manager():
    """Retourne l'instance globale du gestionnaire avancé"""
    return advanced_color_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dialogues modernes pour GSCOM
Interfaces professionnelles et attrayantes pour les formulaires de création
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QScrollArea, QWidget,
                            QLineEdit, QTextEdit, QComboBox, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
                            QGridLayout, QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient

class ModernDialog(QDialog):
    """Dialogue moderne avec design professionnel"""

    def __init__(self, title="Dialogue", subtitle="", icon="", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumSize(800, 600)
        self.setMaximumSize(1200, 900)

        # Propriétés du dialogue
        self.title_text = title
        self.subtitle_text = subtitle
        self.icon_text = icon

        # Configuration de base
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Interface
        self.setup_ui()
        self.apply_modern_styles()

        # Variables pour le déplacement de fenêtre
        self.drag_position = None

    def setup_ui(self):
        """Configure l'interface moderne"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Frame principal avec bordures arrondies
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        main_layout.addWidget(self.main_frame)

        # Layout du frame principal
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(0, 0, 0, 0)
        frame_layout.setSpacing(0)

        # En-tête moderne
        self.create_header(frame_layout)

        # Zone de contenu avec scroll
        self.create_content_area(frame_layout)

        # Pied de page avec boutons
        self.create_footer(frame_layout)

    def create_header(self, layout):
        """Crée l'en-tête moderne"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(25, 15, 25, 15)

        # Icône et textes
        info_layout = QVBoxLayout()

        # Titre principal
        title_label = QLabel(self.title_text)
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        info_layout.addWidget(title_label)

        # Sous-titre
        if self.subtitle_text:
            subtitle_label = QLabel(self.subtitle_text)
            subtitle_label.setObjectName("subtitleLabel")
            subtitle_label.setFont(QFont("Segoe UI", 11))
            info_layout.addWidget(subtitle_label)

        header_layout.addLayout(info_layout)
        header_layout.addStretch()

        # Boutons de contrôle
        controls_layout = QHBoxLayout()

        # Bouton aide
        help_btn = QPushButton("?")
        help_btn.setObjectName("controlButton")
        help_btn.setFixedSize(30, 30)
        help_btn.clicked.connect(self.show_help)
        controls_layout.addWidget(help_btn)

        # Bouton fermer
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeButton")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.reject)
        controls_layout.addWidget(close_btn)

        header_layout.addLayout(controls_layout)
        layout.addWidget(header_frame)

    def create_content_area(self, layout):
        """Crée la zone de contenu avec scroll"""
        # Zone de contenu scrollable
        scroll_area = QScrollArea()
        scroll_area.setObjectName("contentScrollArea")
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget de contenu
        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")

        # Layout de contenu (à surcharger dans les classes filles)
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(25, 20, 25, 20)
        self.content_layout.setSpacing(20)

        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)

    def create_footer(self, layout):
        """Crée le pied de page avec boutons"""
        footer_frame = QFrame()
        footer_frame.setObjectName("footerFrame")
        footer_frame.setFixedHeight(70)

        footer_layout = QHBoxLayout(footer_frame)
        footer_layout.setContentsMargins(25, 15, 25, 15)

        # Spacer pour aligner les boutons à droite
        footer_layout.addStretch()

        # Bouton Annuler
        self.cancel_btn = QPushButton("Annuler")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.setFixedSize(120, 40)
        self.cancel_btn.clicked.connect(self.reject)
        footer_layout.addWidget(self.cancel_btn)

        # Espacement
        footer_layout.addSpacing(10)

        # Bouton Valider
        self.accept_btn = QPushButton("Enregistrer")
        self.accept_btn.setObjectName("acceptButton")
        self.accept_btn.setFixedSize(120, 40)
        self.accept_btn.clicked.connect(self.accept)
        footer_layout.addWidget(self.accept_btn)

        layout.addWidget(footer_frame)

    def create_form_group(self, title, fields):
        """Crée un groupe de champs avec titre"""
        group = QGroupBox(title)
        group.setObjectName("formGroup")

        layout = QGridLayout(group)
        layout.setContentsMargins(20, 25, 20, 20)
        layout.setSpacing(15)
        layout.setColumnStretch(1, 1)  # La colonne des widgets s'étend

        for i, (label_text, widget) in enumerate(fields):
            # Label
            label = QLabel(label_text)
            label.setObjectName("fieldLabel")
            label.setMinimumWidth(120)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            layout.addWidget(label, i, 0)

            # Widget
            widget.setObjectName("fieldWidget")
            widget.setMinimumHeight(35)  # Hauteur minimum pour visibilité
            layout.addWidget(widget, i, 1)

        return group

    def create_field_widget(self, field_type, **kwargs):
        """Crée un widget de champ selon le type"""
        if field_type == "text":
            widget = QLineEdit()
            if "placeholder" in kwargs:
                widget.setPlaceholderText(kwargs["placeholder"])
            if "text" in kwargs:
                widget.setText(kwargs["text"])

        elif field_type == "textarea":
            widget = QTextEdit()
            widget.setMaximumHeight(100)
            if "placeholder" in kwargs:
                widget.setPlaceholderText(kwargs["placeholder"])
            if "text" in kwargs:
                widget.setPlainText(kwargs["text"])

        elif field_type == "combo":
            widget = QComboBox()
            if "items" in kwargs:
                for item in kwargs["items"]:
                    if isinstance(item, tuple):
                        widget.addItem(item[0], item[1])
                    else:
                        widget.addItem(str(item))

        elif field_type == "date":
            widget = QDateEdit()
            widget.setDate(QDate.currentDate())
            widget.setCalendarPopup(True)

        elif field_type == "number":
            widget = QSpinBox()
            widget.setRange(0, 999999)
            if "value" in kwargs:
                widget.setValue(kwargs["value"])

        elif field_type == "decimal":
            widget = QDoubleSpinBox()
            widget.setRange(0.0, 999999.99)
            widget.setDecimals(2)
            if "value" in kwargs:
                widget.setValue(kwargs["value"])

        elif field_type == "checkbox":
            widget = QCheckBox(kwargs.get("text", ""))
            if "checked" in kwargs:
                widget.setChecked(kwargs["checked"])

        else:
            widget = QLineEdit()

        # IMPORTANT: Définir l'objectName pour que les styles s'appliquent
        widget.setObjectName("fieldWidget")
        widget.setMinimumHeight(35)  # Hauteur minimum pour visibilité

        return widget

    def show_help(self):
        """Affiche l'aide (à surcharger)"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Aide",
                               "Remplissez les champs requis et cliquez sur Enregistrer.")

    def mousePressEvent(self, event):
        """Gestion du clic pour déplacer la fenêtre"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Gestion du déplacement de la fenêtre"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def apply_modern_styles(self):
        """Applique les styles modernes"""
        self.setStyleSheet("""
            /* Frame principal */
            #mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(15, 15, 35, 0.98),
                    stop:1 rgba(25, 25, 55, 0.98));
                border: 2px solid rgba(0, 212, 255, 0.3);
                border-radius: 20px;
            }

            /* En-tête */
            #headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 212, 255, 0.15),
                    stop:1 rgba(199, 125, 255, 0.15));
                border-radius: 18px 18px 0 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            #titleLabel {
                color: #ffffff;
                font-weight: bold;
            }

            #subtitleLabel {
                color: rgba(255, 255, 255, 0.7);
            }

            /* Boutons de contrôle */
            #controlButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                color: #ffffff;
                font-weight: bold;
            }

            #controlButton:hover {
                background: rgba(0, 212, 255, 0.3);
                border-color: #00d4ff;
            }

            #closeButton {
                background: rgba(255, 100, 100, 0.2);
                border: 1px solid rgba(255, 100, 100, 0.3);
                border-radius: 15px;
                color: #ffffff;
                font-weight: bold;
            }

            #closeButton:hover {
                background: rgba(255, 100, 100, 0.4);
                border-color: #ff6464;
            }

            /* Zone de contenu */
            #contentScrollArea {
                background: transparent;
                border: none;
            }

            #contentWidget {
                background: transparent;
            }

            /* Groupes de formulaire */
            QGroupBox {
                background: rgba(255, 255, 255, 0.08);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                font-weight: bold;
                color: #00d4ff;
                font-size: 14px;
                margin-top: 15px;
                padding-top: 20px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(0, 212, 255, 0.1));
                border: 1px solid rgba(0, 212, 255, 0.4);
                border-radius: 8px;
                color: #ffffff;
                font-weight: bold;
            }

            /* Labels de champs */
            #fieldLabel {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 600;
                font-size: 13px;
                min-width: 120px;
            }

            /* Widgets de champs - Visibilité maximale */
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                padding: 10px 12px;
                color: #ffffff;
                font-size: 14px;
                min-height: 25px;
                font-weight: 600;
                selection-background-color: rgba(0, 212, 255, 0.5);
            }

            /* Spécifique pour les champs avec objectName */
            *[objectName="fieldWidget"] {
                background: rgba(255, 255, 255, 0.2) !important;
                border: 2px solid rgba(255, 255, 255, 0.4) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
            }

            #fieldWidget:focus, QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border: 2px solid #00d4ff;
                background: rgba(255, 255, 255, 0.22);
                /* box-shadow non supporté par Qt - remplacé par bordure épaisse */
            }

            QLineEdit::placeholder, QTextEdit::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            /* ComboBox */
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }

            QComboBox::down-arrow {
                image: none;
                border: 2px solid #00d4ff;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin-right: 8px;
            }

            /* DateEdit */
            QDateEdit::drop-down {
                border: none;
                width: 30px;
            }

            /* SpinBox */
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background: rgba(0, 212, 255, 0.2);
                border: none;
                border-radius: 4px;
                width: 20px;
            }

            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background: rgba(0, 212, 255, 0.2);
                border: none;
                border-radius: 4px;
                width: 20px;
            }

            /* CheckBox */
            QCheckBox {
                color: rgba(255, 255, 255, 0.9);
                font-size: 13px;
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
            }

            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00d4ff, stop:1 #0099cc);
                border-color: #00d4ff;
            }

            /* Pied de page */
            #footerFrame {
                background: rgba(255, 255, 255, 0.03);
                border-radius: 0 0 18px 18px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            /* Boutons du pied de page */
            #cancelButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                font-weight: 600;
                font-size: 13px;
            }

            #cancelButton:hover {
                background: rgba(255, 255, 255, 0.15);
                border-color: rgba(255, 255, 255, 0.5);
            }

            #acceptButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00d4ff, stop:1 #0099cc);
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
            }

            #acceptButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00b8e6, stop:1 #0088bb);
            }

            #acceptButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #009acc, stop:1 #0077aa);
            }

            /* Barres de défilement */
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }

            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 212, 255, 0.6),
                    stop:1 rgba(0, 212, 255, 0.4));
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 212, 255, 0.8),
                    stop:1 rgba(0, 212, 255, 0.6));
            }

            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0;
                background: transparent;
            }

            /* Styles pour les tableaux */
            QTableWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                font-size: 12px;
            }

            QTableWidget::item {
                background: transparent;
                border: none;
                padding: 8px;
                color: #ffffff;
            }

            QTableWidget::item:selected {
                background: rgba(0, 212, 255, 0.3);
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(0, 212, 255, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 8px;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
            }

            /* Boutons d'action dans les dialogues */
            QPushButton[objectName="actionButton"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(0, 212, 255, 0.2));
                border: 1px solid rgba(0, 212, 255, 0.4);
                border-radius: 6px;
                padding: 8px 15px;
                color: #ffffff;
                font-weight: 600;
                font-size: 12px;
                min-width: 100px;
            }

            QPushButton[objectName="actionButton"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.4),
                    stop:1 rgba(0, 212, 255, 0.3));
                border-color: #00d4ff;
            }
        """)


class ModernClientDialog(ModernDialog):
    """Dialogue moderne pour la création/modification de clients"""

    def __init__(self, parent=None, client=None):
        self.client = client
        self.is_edit_mode = client is not None

        title = "Modifier le client" if self.is_edit_mode else "Nouveau Client"
        subtitle = "Modifiez les informations du client" if self.is_edit_mode else "Créez un nouveau client dans votre base de données"

        super().__init__(title, subtitle, "👥", parent)
        self.setup_client_form()

        if self.is_edit_mode:
            self.load_client_data()

    def setup_client_form(self):
        """Configure le formulaire client"""
        # Informations générales
        general_fields = [
            ("Nom *", self.create_field_widget("text", placeholder="Nom du client")),
            ("Prénom", self.create_field_widget("text", placeholder="Prénom du client")),
            ("Entreprise", self.create_field_widget("text", placeholder="Nom de l'entreprise")),
            ("Email", self.create_field_widget("text", placeholder="<EMAIL>")),
            ("Téléphone", self.create_field_widget("text", placeholder="+213 XX XX XX XX")),
        ]

        self.name_input = general_fields[0][1]
        self.first_name_input = general_fields[1][1]
        self.company_input = general_fields[2][1]
        self.email_input = general_fields[3][1]
        self.phone_input = general_fields[4][1]

        general_group = self.create_form_group("📋 Informations Générales", general_fields)
        self.content_layout.addWidget(general_group)

        # Adresse
        address_fields = [
            ("Adresse", self.create_field_widget("textarea", placeholder="Adresse complète")),
            ("Ville", self.create_field_widget("text", placeholder="Ville")),
            ("Code postal", self.create_field_widget("text", placeholder="Code postal")),
            ("Pays", self.create_field_widget("text", text="Algérie")),
        ]

        self.address_input = address_fields[0][1]
        self.city_input = address_fields[1][1]
        self.postal_code_input = address_fields[2][1]
        self.country_input = address_fields[3][1]

        address_group = self.create_form_group("📍 Adresse", address_fields)
        self.content_layout.addWidget(address_group)

        # Informations commerciales
        commercial_fields = [
            ("Type de client", self.create_field_widget("combo", items=[
                ("Particulier", "individual"),
                ("Entreprise", "company"),
                ("Administration", "government")
            ])),
            ("Statut", self.create_field_widget("combo", items=[
                ("Actif", "active"),
                ("Inactif", "inactive"),
                ("Prospect", "prospect")
            ])),
            ("Limite de crédit", self.create_field_widget("decimal", value=0.0)),
        ]

        self.client_type_input = commercial_fields[0][1]
        self.status_input = commercial_fields[1][1]
        self.credit_limit_input = commercial_fields[2][1]

        commercial_group = self.create_form_group("💼 Informations Commerciales", commercial_fields)
        self.content_layout.addWidget(commercial_group)

        # Notes
        notes_fields = [
            ("Notes", self.create_field_widget("textarea", placeholder="Notes et commentaires sur le client...")),
        ]

        self.notes_input = notes_fields[0][1]
        notes_group = self.create_form_group("📝 Notes", notes_fields)
        self.content_layout.addWidget(notes_group)

    def load_client_data(self):
        """Charge les données du client en mode édition"""
        if self.client:
            self.name_input.setText(self.client.name or "")
            self.first_name_input.setText(getattr(self.client, 'first_name', '') or "")
            self.company_input.setText(getattr(self.client, 'company', '') or "")
            self.email_input.setText(self.client.email or "")
            self.phone_input.setText(self.client.phone or "")
            self.address_input.setPlainText(self.client.address or "")
            self.city_input.setText(getattr(self.client, 'city', '') or "")
            self.postal_code_input.setText(getattr(self.client, 'postal_code', '') or "")
            self.country_input.setText(getattr(self.client, 'country', 'Algérie') or "Algérie")
            self.notes_input.setPlainText(getattr(self.client, 'notes', '') or "")

    def get_data(self):
        """Récupère les données du formulaire"""
        return {
            'name': self.name_input.text().strip(),
            'first_name': self.first_name_input.text().strip(),
            'company': self.company_input.text().strip(),
            'email': self.email_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'address': self.address_input.toPlainText().strip(),
            'city': self.city_input.text().strip(),
            'postal_code': self.postal_code_input.text().strip(),
            'country': self.country_input.text().strip(),
            'client_type': self.client_type_input.currentData(),
            'status': self.status_input.currentData(),
            'credit_limit': self.credit_limit_input.value(),
            'notes': self.notes_input.toPlainText().strip()
        }

    def accept(self):
        """Validation avant acceptation"""
        if not self.name_input.text().strip():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Validation", "Le nom du client est obligatoire.")
            self.name_input.setFocus()
            return

        super().accept()


class ModernProductDialog(ModernDialog):
    """Dialogue moderne pour la création/modification de produits"""

    def __init__(self, categories=None, units=None, parent=None, product=None):
        self.product = product
        self.categories = categories or []
        self.units = units or []
        self.is_edit_mode = product is not None

        title = "Modifier le produit" if self.is_edit_mode else "Nouveau Produit"
        subtitle = "Modifiez les informations du produit" if self.is_edit_mode else "Ajoutez un nouveau produit à votre catalogue"

        super().__init__(title, subtitle, "📦", parent)
        self.setup_product_form()

        if self.is_edit_mode:
            self.load_product_data()

    def setup_product_form(self):
        """Configure le formulaire produit"""
        # Informations de base
        basic_fields = [
            ("Nom *", self.create_field_widget("text", placeholder="Nom du produit")),
            ("Code", self.create_field_widget("text", placeholder="Code automatique si vide")),
            ("Code-barres", self.create_field_widget("text", placeholder="Code-barres EAN/UPC")),
            ("Référence", self.create_field_widget("text", placeholder="Référence interne")),
        ]

        self.name_input = basic_fields[0][1]
        self.code_input = basic_fields[1][1]
        self.barcode_input = basic_fields[2][1]
        self.reference_input = basic_fields[3][1]

        basic_group = self.create_form_group("📋 Informations de Base", basic_fields)
        self.content_layout.addWidget(basic_group)

        # Classification
        category_items = [("Sélectionner une catégorie", None)]
        for cat in self.categories:
            if isinstance(cat, dict):
                category_items.append((cat['name'], cat['id']))
            else:
                category_items.append((cat.name, cat.id))

        unit_items = [("Sélectionner une unité", None)]
        for unit in self.units:
            if isinstance(unit, dict):
                symbol = unit.get('symbol', '')
                name = unit['name']
                display_name = f"{name} ({symbol})" if symbol else name
                unit_items.append((display_name, unit['id']))
            else:
                symbol = getattr(unit, 'symbol', '')
                name = unit.name
                display_name = f"{name} ({symbol})" if symbol else name
                unit_items.append((display_name, unit.id))

        classification_fields = [
            ("Catégorie", self.create_field_widget("combo", items=category_items)),
            ("Unité", self.create_field_widget("combo", items=unit_items)),
            ("Type", self.create_field_widget("combo", items=[
                ("Produit", "product"),
                ("Service", "service"),
                ("Matière première", "raw_material")
            ])),
        ]

        self.category_input = classification_fields[0][1]
        self.unit_input = classification_fields[1][1]
        self.product_type_input = classification_fields[2][1]

        classification_group = self.create_form_group("🏷️ Classification", classification_fields)
        self.content_layout.addWidget(classification_group)

        # Prix et stock
        pricing_fields = [
            ("Prix d'achat", self.create_field_widget("decimal", value=0.0)),
            ("Prix de vente", self.create_field_widget("decimal", value=0.0)),
            ("Stock initial", self.create_field_widget("number", value=0)),
            ("Stock minimum", self.create_field_widget("number", value=0)),
            ("TVA (%)", self.create_field_widget("decimal", value=19.0)),
        ]

        self.purchase_price_input = pricing_fields[0][1]
        self.sale_price_input = pricing_fields[1][1]
        self.initial_stock_input = pricing_fields[2][1]
        self.min_stock_input = pricing_fields[3][1]
        self.tax_rate_input = pricing_fields[4][1]

        pricing_group = self.create_form_group("💰 Prix et Stock", pricing_fields)
        self.content_layout.addWidget(pricing_group)

        # Description
        description_fields = [
            ("Description", self.create_field_widget("textarea", placeholder="Description détaillée du produit...")),
        ]

        self.description_input = description_fields[0][1]
        description_group = self.create_form_group("📝 Description", description_fields)
        self.content_layout.addWidget(description_group)

    def load_product_data(self):
        """Charge les données du produit en mode édition"""
        if self.product:
            self.name_input.setText(self.product.name or "")
            self.code_input.setText(getattr(self.product, 'code', '') or "")
            self.barcode_input.setText(getattr(self.product, 'barcode', '') or "")
            self.reference_input.setText(getattr(self.product, 'reference', '') or "")
            self.description_input.setPlainText(getattr(self.product, 'description', '') or "")

            # Sélectionner la catégorie
            if hasattr(self.product, 'category_id') and self.product.category_id:
                for i in range(self.category_input.count()):
                    if self.category_input.itemData(i) == self.product.category_id:
                        self.category_input.setCurrentIndex(i)
                        break

            # Sélectionner l'unité
            if hasattr(self.product, 'unit_id') and self.product.unit_id:
                for i in range(self.unit_input.count()):
                    if self.unit_input.itemData(i) == self.product.unit_id:
                        self.unit_input.setCurrentIndex(i)
                        break

    def get_data(self):
        """Récupère les données du formulaire"""
        return {
            'name': self.name_input.text().strip(),
            'code': self.code_input.text().strip(),
            'barcode': self.barcode_input.text().strip(),
            'reference': self.reference_input.text().strip(),
            'description': self.description_input.toPlainText().strip(),
            'category_id': self.category_input.currentData(),
            'unit_id': self.unit_input.currentData(),
            'product_type': self.product_type_input.currentData(),
            'purchase_price': self.purchase_price_input.value(),
            'sale_price': self.sale_price_input.value(),
            'initial_stock': self.initial_stock_input.value(),
            'min_stock': self.min_stock_input.value(),
            'tax_rate': self.tax_rate_input.value()
        }

    def accept(self):
        """Validation avant acceptation"""
        if not self.name_input.text().strip():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Validation", "Le nom du produit est obligatoire.")
            self.name_input.setFocus()
            return

        super().accept()


class ModernQuoteDialog(ModernDialog):
    """Dialogue moderne pour la création/modification de devis"""

    def __init__(self, parent=None, quote=None):
        self.quote = quote
        self.is_edit_mode = quote is not None

        title = "Modifier le devis" if self.is_edit_mode else "Nouveau Devis"
        subtitle = "Modifiez les informations du devis" if self.is_edit_mode else "Créez un nouveau devis pour votre client"

        super().__init__(title, subtitle, "📝", parent)
        self.setup_quote_form()

        if self.is_edit_mode:
            self.load_quote_data()

    def setup_quote_form(self):
        """Configure le formulaire devis"""
        # Informations générales
        general_fields = [
            ("Client *", self.create_field_widget("combo", items=[("Sélectionner un client", None)])),
            ("Numéro", self.create_field_widget("text", placeholder="Numéro automatique si vide")),
            ("Date", self.create_field_widget("date")),
            ("Valide jusqu'au", self.create_field_widget("date")),
        ]

        self.client_input = general_fields[0][1]
        self.number_input = general_fields[1][1]
        self.date_input = general_fields[2][1]
        self.valid_until_input = general_fields[3][1]

        # Définir la date de validité par défaut (30 jours)
        from PyQt5.QtCore import QDate
        self.valid_until_input.setDate(QDate.currentDate().addDays(30))

        general_group = self.create_form_group("📋 Informations Générales", general_fields)
        self.content_layout.addWidget(general_group)

        # Lignes du devis
        lines_group = QGroupBox("📦 Lignes du Devis")
        lines_group.setObjectName("formGroup")
        lines_layout = QVBoxLayout(lines_group)
        lines_layout.setContentsMargins(20, 25, 20, 20)

        # Tableau des lignes (simplifié pour l'exemple)
        from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        self.lines_table = QTableWidget(0, 5)
        self.lines_table.setHorizontalHeaderLabels(["Produit", "Description", "Quantité", "Prix Unit.", "Total"])
        self.lines_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.lines_table.setMaximumHeight(200)
        self.lines_table.setObjectName("linesTable")

        lines_layout.addWidget(self.lines_table)

        # Boutons pour gérer les lignes
        lines_buttons_layout = QHBoxLayout()

        add_line_btn = QPushButton("➕ Ajouter ligne")
        add_line_btn.setObjectName("actionButton")
        add_line_btn.clicked.connect(self.add_line)
        lines_buttons_layout.addWidget(add_line_btn)

        remove_line_btn = QPushButton("➖ Supprimer ligne")
        remove_line_btn.setObjectName("actionButton")
        remove_line_btn.clicked.connect(self.remove_line)
        lines_buttons_layout.addWidget(remove_line_btn)

        lines_buttons_layout.addStretch()
        lines_layout.addLayout(lines_buttons_layout)

        self.content_layout.addWidget(lines_group)

        # Totaux
        totals_fields = [
            ("Sous-total HT", self.create_field_widget("decimal", value=0.0)),
            ("TVA", self.create_field_widget("decimal", value=0.0)),
            ("Total TTC", self.create_field_widget("decimal", value=0.0)),
        ]

        self.subtotal_input = totals_fields[0][1]
        self.tax_input = totals_fields[1][1]
        self.total_input = totals_fields[2][1]

        # Rendre les totaux en lecture seule
        self.subtotal_input.setReadOnly(True)
        self.tax_input.setReadOnly(True)
        self.total_input.setReadOnly(True)

        totals_group = self.create_form_group("💰 Totaux", totals_fields)
        self.content_layout.addWidget(totals_group)

        # Notes
        notes_fields = [
            ("Notes", self.create_field_widget("textarea", placeholder="Notes et conditions du devis...")),
        ]

        self.notes_input = notes_fields[0][1]
        notes_group = self.create_form_group("📝 Notes", notes_fields)
        self.content_layout.addWidget(notes_group)

        # Charger les clients
        self.load_clients()

    def load_clients(self):
        """Charge la liste des clients"""
        try:
            from src.dal.database import db_manager
            from src.dal.models.client import Client

            with db_manager.get_session() as session:
                clients = session.query(Client).all()

                self.client_input.clear()
                self.client_input.addItem("Sélectionner un client", None)

                for client in clients:
                    display_name = f"{client.name}"
                    if hasattr(client, 'company') and client.company:
                        display_name += f" ({client.company})"
                    self.client_input.addItem(display_name, client.id)

        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")

    def add_line(self):
        """Ajoute une ligne au devis"""
        row = self.lines_table.rowCount()
        self.lines_table.insertRow(row)

        # Ajouter des cellules vides
        from PyQt5.QtWidgets import QTableWidgetItem
        for col in range(5):
            item = QTableWidgetItem("")
            self.lines_table.setItem(row, col, item)

        # Valeurs par défaut
        self.lines_table.setItem(row, 2, QTableWidgetItem("1"))  # Quantité
        self.lines_table.setItem(row, 3, QTableWidgetItem("0.00"))  # Prix
        self.lines_table.setItem(row, 4, QTableWidgetItem("0.00"))  # Total

    def remove_line(self):
        """Supprime la ligne sélectionnée"""
        current_row = self.lines_table.currentRow()
        if current_row >= 0:
            self.lines_table.removeRow(current_row)

    def load_quote_data(self):
        """Charge les données du devis en mode édition"""
        if self.quote:
            self.number_input.setText(getattr(self.quote, 'number', '') or "")

            if hasattr(self.quote, 'date') and self.quote.date:
                from PyQt5.QtCore import QDate
                self.date_input.setDate(QDate.fromString(str(self.quote.date), "yyyy-MM-dd"))

            if hasattr(self.quote, 'valid_until') and self.quote.valid_until:
                from PyQt5.QtCore import QDate
                self.valid_until_input.setDate(QDate.fromString(str(self.quote.valid_until), "yyyy-MM-dd"))

            # Sélectionner le client
            if hasattr(self.quote, 'client_id') and self.quote.client_id:
                for i in range(self.client_input.count()):
                    if self.client_input.itemData(i) == self.quote.client_id:
                        self.client_input.setCurrentIndex(i)
                        break

            self.notes_input.setPlainText(getattr(self.quote, 'notes', '') or "")

    def get_data(self):
        """Récupère les données du formulaire"""
        # Collecter les lignes
        lines_data = []
        for row in range(self.lines_table.rowCount()):
            line_data = {
                'product_name': self.lines_table.item(row, 0).text() if self.lines_table.item(row, 0) else "",
                'description': self.lines_table.item(row, 1).text() if self.lines_table.item(row, 1) else "",
                'quantity': float(self.lines_table.item(row, 2).text() or "0"),
                'unit_price': float(self.lines_table.item(row, 3).text() or "0"),
                'total': float(self.lines_table.item(row, 4).text() or "0")
            }
            lines_data.append(line_data)

        return {
            'client_id': self.client_input.currentData(),
            'number': self.number_input.text().strip(),
            'date': self.date_input.date().toPyDate(),
            'valid_until': self.valid_until_input.date().toPyDate(),
            'notes': self.notes_input.toPlainText().strip(),
            'lines': lines_data,
            'subtotal': self.subtotal_input.value(),
            'tax_amount': self.tax_input.value(),
            'total': self.total_input.value()
        }

    def accept(self):
        """Validation avant acceptation"""
        if not self.client_input.currentData():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Validation", "Veuillez sélectionner un client.")
            self.client_input.setFocus()
            return

        super().accept()

    def show_help(self):
        """Affiche l'aide spécifique aux devis"""
        from PyQt5.QtWidgets import QMessageBox
        help_text = """
        📝 Aide - Création de Devis

        1. Sélectionnez un client existant
        2. Le numéro sera généré automatiquement si laissé vide
        3. Ajustez les dates selon vos besoins
        4. Ajoutez des lignes de produits/services
        5. Les totaux seront calculés automatiquement
        6. Ajoutez des notes si nécessaire

        💡 Astuce: Utilisez les boutons ➕ et ➖ pour gérer les lignes
        """
        QMessageBox.information(self, "Aide", help_text)
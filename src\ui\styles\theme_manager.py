#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de thème global pour GSCOM
Application cohérente des couleurs améliorées dans toute l'application
"""

import logging
import datetime
import json
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal, QSettings

from .unified_color_system import (
    get_unified_color_palette,
    get_complete_unified_styles
)

class ThemeManager(QObject):
    """Gestionnaire global des thèmes GSCOM avec persistance"""

    theme_changed = pyqtSignal(str)  # Signal émis lors du changement de thème

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings = QSettings("GSCOM", "ThemeSettings")
        self.color_palette = get_unified_color_palette()

        # Charger le thème sauvegardé ou utiliser le défaut
        self.current_theme = self.load_saved_theme()
        self.logger.info(f"Thème initialisé: {self.current_theme}")

    def load_saved_theme(self):
        """Charge le thème sauvegardé ou retourne le thème par défaut"""
        try:
            saved_theme = self.settings.value("current_theme", "dark")
            if saved_theme in ["dark", "light", "auto"]:
                return saved_theme
            else:
                return "dark"
        except Exception as e:
            self.logger.warning(f"Erreur lors du chargement du thème: {e}")
            return "dark"

    def save_theme(self, theme_name):
        """Sauvegarde le thème actuel"""
        try:
            self.settings.setValue("current_theme", theme_name)
            self.settings.sync()
            self.logger.debug(f"Thème sauvegardé: {theme_name}")
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du thème: {e}")

    def set_theme(self, theme_name):
        """Définit le thème actuel avec sauvegarde automatique"""
        if theme_name in ["dark", "light", "auto"]:
            old_theme = self.current_theme
            self.current_theme = theme_name

            # Sauvegarder le nouveau thème
            self.save_theme(theme_name)

            # Appliquer le thème
            self.apply_global_theme()

            # Émettre le signal de changement
            self.theme_changed.emit(theme_name)

            self.logger.info(f"Thème changé de '{old_theme}' vers '{theme_name}'")
        else:
            self.logger.warning(f"Thème inconnu: {theme_name}")

    def toggle_theme(self):
        """Bascule entre les thèmes sombre et clair"""
        if self.current_theme == "dark":
            self.set_theme("light")
        elif self.current_theme == "light":
            self.set_theme("auto")
        else:  # auto
            self.set_theme("dark")

        return self.current_theme

    def cycle_theme(self):
        """Fait défiler les thèmes dans l'ordre: dark -> light -> auto -> dark"""
        themes = ["dark", "light", "auto"]
        current_index = themes.index(self.current_theme)
        next_index = (current_index + 1) % len(themes)
        next_theme = themes[next_index]
        self.set_theme(next_theme)
        return next_theme

    def get_current_theme(self):
        """Retourne le thème actuel"""
        return self.current_theme

    def apply_global_theme(self):
        """Applique le thème à toute l'application"""
        app = QApplication.instance()
        if app:
            styles = self.get_theme_styles()
            app.setStyleSheet(styles)

            # Forcer la mise à jour de tous les widgets
            for widget in app.allWidgets():
                if widget:
                    widget.style().unpolish(widget)
                    widget.style().polish(widget)
                    widget.update()

            self.logger.info(f"Thème global '{self.current_theme}' appliqué à l'application")
        else:
            self.logger.warning("Aucune instance QApplication trouvée")

    def get_theme_styles(self):
        """Retourne les styles CSS pour le thème actuel"""
        base_styles = get_complete_unified_styles()

        # Ajouter les classes CSS pour le thème
        theme_class = f"""
            /* ===== THÈME ACTUEL: {self.current_theme.upper()} ===== */
            QWidget {{
                /* Classe de thème pour identification */
            }}
        """

        if self.current_theme == "light":
            return theme_class + base_styles + self.get_light_theme_overrides()
        elif self.current_theme == "auto":
            return theme_class + base_styles + self.get_auto_theme_styles()
        else:  # dark theme (default)
            return theme_class + base_styles + self.get_dark_theme_enhancements()

    def get_theme_name_display(self):
        """Retourne le nom d'affichage du thème actuel"""
        theme_names = {
            "dark": "🌙 Sombre",
            "light": "☀️ Clair",
            "auto": "🔄 Automatique"
        }
        return theme_names.get(self.current_theme, "🌙 Sombre")

    def get_dark_theme_enhancements(self):
        """Améliorations spécifiques au thème sombre"""
        colors = self.color_palette

        return f"""
            /* ===== AMÉLIORATIONS THÈME SOMBRE ===== */

            /* Ombres et effets de profondeur */
            #mainFrame, QDialog {{
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            }}

            #navButton:hover {{
                box-shadow: 0 4px 12px rgba(70, 130, 255, 0.2);
            }}

            /* Barres de défilement sombres */
            QScrollBar:vertical {{
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }}

            QScrollBar::handle:vertical {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {colors['primary_blue']},
                    stop:1 {colors['secondary_blue']});
                border-radius: 6px;
                min-height: 25px;
            }}

            QScrollBar::handle:vertical:hover {{
                background: {colors['secondary_blue']};
            }}

            /* Tooltips sombres */
            QToolTip {{
                background: {colors['bg_secondary']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border_primary']};
                border-radius: 6px;
                padding: 8px;
                font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            }}

            /* Menus contextuels */
            QMenu {{
                background: {colors['bg_secondary']};
                color: {colors['text_primary']};
                border: 2px solid {colors['border_primary']};
                border-radius: 8px;
                padding: 5px;
            }}

            QMenu::item {{
                background: transparent;
                padding: 8px 16px;
                border-radius: 4px;
            }}

            QMenu::item:selected {{
                background: {colors['hover_primary']};
            }}

            /* Barres de statut */
            QStatusBar {{
                background: {colors['bg_tertiary']};
                color: {colors['text_secondary']};
                border-top: 1px solid {colors['border_secondary']};
            }}
        """

    def get_light_theme_overrides(self):
        """Surcharges pour le thème clair"""
        return """
            /* ===== THÈME CLAIR ===== */
            QDialog, QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 0.98),
                    stop:1 rgba(255, 255, 255, 0.98));
                color: #2c3e50;
            }

            #sidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(240, 245, 250, 0.98),
                    stop:1 rgba(248, 250, 252, 0.98));
                border-right-color: rgba(70, 130, 255, 0.2);
            }

            #navTitle {
                color: #2c3e50;
            }

            #navIcon {
                color: #3498db;
            }

            #navButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(52, 152, 219, 0.15),
                    stop:1 rgba(52, 152, 219, 0.08));
            }

            #navButton:hover #navTitle {
                color: #2c3e50;
            }

            #navButton:hover #navIcon {
                color: #2980b9;
            }

            #fieldLabel {
                color: #2c3e50;
            }

            QGroupBox {
                color: #3498db;
            }

            QTableWidget {
                background: rgba(255, 255, 255, 0.98);
                color: #2c3e50;
            }

            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #5dade2);
            }
        """

    def get_auto_theme_styles(self):
        """Styles pour le thème automatique (basé sur l'heure)"""
        import datetime
        current_hour = datetime.datetime.now().hour

        # Thème sombre de 18h à 8h, clair de 8h à 18h
        if 18 <= current_hour or current_hour < 8:
            return self.get_dark_theme_enhancements()
        else:
            return self.get_light_theme_overrides()

    def apply_to_widget(self, widget):
        """Applique le thème à un widget spécifique"""
        if widget:
            styles = self.get_theme_styles()
            widget.setStyleSheet(styles)
            self.logger.debug(f"Thème appliqué au widget: {widget.__class__.__name__}")

    def get_color(self, color_name):
        """Retourne une couleur spécifique de la palette"""
        return self.color_palette.get(color_name, '#ffffff')

    def get_contrast_color(self, background_color):
        """Retourne une couleur de texte contrastée pour un fond donné"""
        # Logique simplifiée pour déterminer si le fond est clair ou sombre
        if 'rgba(255' in background_color or '#f' in background_color.lower():
            return self.color_palette['text_on_light']
        else:
            return self.color_palette['text_primary']

    def create_custom_palette(self, **custom_colors):
        """Crée une palette personnalisée en surchargeant certaines couleurs"""
        custom_palette = self.color_palette.copy()
        custom_palette.update(custom_colors)
        return custom_palette

    def export_theme_config(self):
        """Exporte la configuration du thème actuel"""
        return {
            'theme': self.current_theme,
            'palette': self.color_palette,
            'timestamp': datetime.datetime.now().isoformat()
        }

    def import_theme_config(self, config):
        """Importe une configuration de thème"""
        try:
            if 'theme' in config:
                self.set_theme(config['theme'])
            if 'palette' in config:
                self.color_palette.update(config['palette'])
            self.logger.info("Configuration de thème importée")
            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de l'import du thème: {e}")
            return False

# Instance globale du gestionnaire de thème
theme_manager = ThemeManager()

def get_theme_manager():
    """Retourne l'instance globale du gestionnaire de thème"""
    return theme_manager

def apply_unified_theme(widget=None):
    """Fonction utilitaire pour appliquer le thème unifié"""
    if widget:
        theme_manager.apply_to_widget(widget)
    else:
        theme_manager.apply_global_theme()

def set_global_theme(theme_name):
    """Fonction utilitaire pour changer le thème global"""
    theme_manager.set_theme(theme_name)

def get_current_theme():
    """Fonction utilitaire pour obtenir le thème actuel"""
    return theme_manager.get_current_theme()

def get_theme_color(color_name):
    """Fonction utilitaire pour obtenir une couleur du thème"""
    return theme_manager.get_color(color_name)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Applicateur automatique de couleurs améliorées GSCOM
Système intelligent pour appliquer les couleurs unifiées aux composants existants
"""

import logging
from PyQt5.QtWidgets import QWidget, QDialog, QMainWindow, QTableWidget, QFrame, QLabel, QPushButton
from PyQt5.QtCore import QObject, QTimer

from .unified_color_system import get_complete_unified_styles
from .module_color_enhancements import get_complete_module_styles
from .theme_manager import get_theme_manager

class AutoColorApplier(QObject):
    """Applicateur automatique de couleurs pour les widgets GSCOM"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.applied_widgets = set()  # Éviter les applications multiples
        
    def apply_to_widget(self, widget, force=False):
        """Applique les couleurs améliorées à un widget spécifique"""
        if not widget or (id(widget) in self.applied_widgets and not force):
            return
        
        try:
            # Obtenir les styles complets
            unified_styles = get_complete_unified_styles()
            module_styles = get_complete_module_styles()
            complete_styles = unified_styles + module_styles
            
            # Appliquer les styles
            widget.setStyleSheet(complete_styles)
            
            # Marquer comme appliqué
            self.applied_widgets.add(id(widget))
            
            # Appliquer aux enfants si nécessaire
            self.apply_to_children(widget)
            
            self.logger.debug(f"Couleurs appliquées à {widget.__class__.__name__}")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application des couleurs: {e}")
    
    def apply_to_children(self, parent_widget):
        """Applique les couleurs aux widgets enfants"""
        try:
            # Appliquer aux enfants directs
            for child in parent_widget.findChildren(QWidget):
                if self.should_apply_to_child(child):
                    self.apply_specific_styles(child)
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application aux enfants: {e}")
    
    def should_apply_to_child(self, widget):
        """Détermine si les styles doivent être appliqués à un widget enfant"""
        # Types de widgets qui bénéficient des styles spécifiques
        target_types = (QFrame, QLabel, QPushButton, QTableWidget)
        return isinstance(widget, target_types) and widget.objectName()
    
    def apply_specific_styles(self, widget):
        """Applique des styles spécifiques selon le type de widget"""
        try:
            object_name = widget.objectName()
            
            # Styles spécifiques pour les cartes du dashboard
            if 'card' in object_name.lower() or 'Card' in object_name:
                self.apply_card_styles(widget)
            
            # Styles spécifiques pour les boutons
            elif isinstance(widget, QPushButton):
                self.apply_button_styles(widget, object_name)
            
            # Styles spécifiques pour les tableaux
            elif isinstance(widget, QTableWidget):
                self.apply_table_styles(widget)
            
            # Styles spécifiques pour les labels
            elif isinstance(widget, QLabel):
                self.apply_label_styles(widget, object_name)
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application de styles spécifiques: {e}")
    
    def apply_card_styles(self, widget):
        """Applique les styles aux cartes du dashboard"""
        widget.setProperty("class", "dashboard-card")
        
    def apply_button_styles(self, widget, object_name):
        """Applique les styles aux boutons selon leur type"""
        if 'primary' in object_name.lower() or 'accept' in object_name.lower():
            widget.setObjectName("primaryButton")
        elif 'secondary' in object_name.lower() or 'cancel' in object_name.lower():
            widget.setObjectName("secondaryButton")
        elif 'action' in object_name.lower():
            widget.setObjectName("actionButton")
    
    def apply_table_styles(self, widget):
        """Applique les styles aux tableaux"""
        widget.setProperty("class", "enhanced-table")
        
    def apply_label_styles(self, widget, object_name):
        """Applique les styles aux labels selon leur fonction"""
        if 'title' in object_name.lower():
            widget.setProperty("class", "title-label")
        elif 'field' in object_name.lower():
            widget.setObjectName("fieldLabel")
        elif 'welcome' in object_name.lower():
            widget.setObjectName("welcomeLabel")
    
    def auto_detect_and_apply(self, root_widget):
        """Détecte automatiquement les composants et applique les couleurs"""
        try:
            # Appliquer au widget racine
            self.apply_to_widget(root_widget)
            
            # Détecter et traiter les composants spécifiques
            self.detect_dashboard_components(root_widget)
            self.detect_dialog_components(root_widget)
            self.detect_table_components(root_widget)
            
            self.logger.info(f"Application automatique terminée pour {root_widget.__class__.__name__}")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la détection automatique: {e}")
    
    def detect_dashboard_components(self, widget):
        """Détecte et améliore les composants du dashboard"""
        try:
            # Rechercher les cartes de statistiques
            stat_cards = widget.findChildren(QFrame, "dashboardCard")
            for card in stat_cards:
                self.enhance_dashboard_card(card)
            
            # Rechercher les labels de bienvenue
            welcome_labels = widget.findChildren(QLabel)
            for label in welcome_labels:
                if "bienvenue" in label.text().lower() or "dashboard" in label.text().lower():
                    label.setObjectName("welcomeLabel")
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de la détection du dashboard: {e}")
    
    def enhance_dashboard_card(self, card):
        """Améliore une carte du dashboard"""
        try:
            # Rechercher les composants de la carte
            labels = card.findChildren(QLabel)
            
            for i, label in enumerate(labels):
                text = label.text().lower()
                
                # Identifier le type de label
                if i == 0 or any(emoji in label.text() for emoji in ['💰', '📋', '👥', '📦', '🧾']):
                    label.setObjectName("cardIcon")
                elif any(word in text for word in ['chiffre', 'commandes', 'clients', 'produits', 'factures']):
                    label.setObjectName("cardTitle")
                elif any(char.isdigit() for char in label.text()):
                    label.setObjectName("cardValue")
                else:
                    label.setObjectName("cardDescription")
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de l'amélioration de la carte: {e}")
    
    def detect_dialog_components(self, widget):
        """Détecte et améliore les composants des dialogues"""
        try:
            if isinstance(widget, QDialog):
                # Améliorer la structure du dialogue
                self.enhance_dialog_structure(widget)
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la détection du dialogue: {e}")
    
    def enhance_dialog_structure(self, dialog):
        """Améliore la structure d'un dialogue"""
        try:
            # Rechercher et améliorer les boutons
            buttons = dialog.findChildren(QPushButton)
            for button in buttons:
                text = button.text().lower()
                if any(word in text for word in ['ok', 'valider', 'enregistrer', 'accepter']):
                    button.setObjectName("dialogAcceptButton")
                elif any(word in text for word in ['annuler', 'fermer', 'cancel']):
                    button.setObjectName("dialogCancelButton")
            
            # Rechercher et améliorer les labels de champs
            labels = dialog.findChildren(QLabel)
            for label in labels:
                if label.buddy():  # Label associé à un champ
                    label.setObjectName("fieldLabel")
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de l'amélioration du dialogue: {e}")
    
    def detect_table_components(self, widget):
        """Détecte et améliore les composants des tableaux"""
        try:
            tables = widget.findChildren(QTableWidget)
            for table in tables:
                self.enhance_table(table)
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la détection des tableaux: {e}")
    
    def enhance_table(self, table):
        """Améliore un tableau"""
        try:
            # Appliquer les styles de tableau améliorés
            table.setProperty("class", "enhanced-table")
            
            # Améliorer les en-têtes si possible
            if table.horizontalHeader():
                table.horizontalHeader().setProperty("class", "enhanced-header")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'amélioration du tableau: {e}")
    
    def schedule_delayed_application(self, widget, delay_ms=100):
        """Programme une application retardée des couleurs"""
        def apply_delayed():
            self.apply_to_widget(widget, force=True)
        
        QTimer.singleShot(delay_ms, apply_delayed)
    
    def clear_applied_cache(self):
        """Vide le cache des widgets traités"""
        self.applied_widgets.clear()
        self.logger.debug("Cache des widgets appliqués vidé")

# Instance globale de l'applicateur
auto_color_applier = AutoColorApplier()

def apply_enhanced_colors(widget, auto_detect=True):
    """Fonction utilitaire pour appliquer les couleurs améliorées"""
    if auto_detect:
        auto_color_applier.auto_detect_and_apply(widget)
    else:
        auto_color_applier.apply_to_widget(widget)

def apply_enhanced_colors_delayed(widget, delay_ms=100):
    """Fonction utilitaire pour appliquer les couleurs avec délai"""
    auto_color_applier.schedule_delayed_application(widget, delay_ms)

def enhance_existing_component(widget):
    """Améliore un composant existant avec les nouvelles couleurs"""
    auto_color_applier.auto_detect_and_apply(widget)

def clear_color_cache():
    """Vide le cache des couleurs appliquées"""
    auto_color_applier.clear_applied_cache()

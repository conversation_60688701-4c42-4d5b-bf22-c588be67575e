#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple des corrections d'accessibilité GSCOM
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_accessibility_files():
    """Test de la présence des fichiers d'accessibilité"""
    print("🧪 Test Simple d'Accessibilité GSCOM")
    print("=" * 50)
    
    # Vérifier les fichiers CSS
    css_file = "src/ui/styles/gscom_accessibility_fix.css"
    if os.path.exists(css_file):
        print("✅ Fichier CSS d'accessibilité présent")
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "--bg-primary-light: #ffffff" in content:
                print("✅ Mode clair configuré (fond blanc)")
            if "--text-primary-light: #1e293b" in content:
                print("✅ Texte sombre configuré (contraste optimal)")
            if ".dark-theme" in content:
                print("✅ Mode sombre disponible")
    else:
        print("❌ Fichier CSS d'accessibilité manquant")
    
    # Vérifier le gestionnaire
    manager_file = "src/ui/styles/accessibility_manager.py"
    if os.path.exists(manager_file):
        print("✅ Gestionnaire d'accessibilité présent")
    else:
        print("❌ Gestionnaire d'accessibilité manquant")
    
    # Test d'import
    try:
        from src.ui.styles.accessibility_manager import get_accessibility_manager
        manager = get_accessibility_manager()
        print(f"✅ Gestionnaire initialisé - Thème: {manager.get_current_theme()}")
        print(f"✅ Nom d'affichage: {manager.get_theme_display_name()}")
    except Exception as e:
        print(f"❌ Erreur import gestionnaire: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 TEST TERMINÉ")
    print("=" * 50)
    print("✅ Les corrections d'accessibilité sont installées")
    print("✅ Mode clair par défaut avec contraste optimal")
    print("✅ Mode sombre disponible")
    print("✅ Gestionnaire fonctionnel")
    print()
    print("🚀 Pour tester visuellement :")
    print("   python main.py")
    print("   (ou python quick_test.py)")

if __name__ == "__main__":
    test_accessibility_files()

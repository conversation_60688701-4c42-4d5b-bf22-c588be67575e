#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour diagnostiquer et corriger les problèmes de visibilité des dialogues
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_modern_dialogs():
    """Test des dialogues modernes"""
    print("🧪 Test des dialogues modernes GSCOM\n")

    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt

        app = QApplication(sys.argv)

        # Fenêtre de test
        window = QMainWindow()
        window.setWindowTitle("Test Dialogues GSCOM")
        window.setGeometry(100, 100, 400, 300)

        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)

        # Test dialogue client
        def test_client_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernClientDialog
                dialog = ModernClientDialog(window)
                print("✅ ModernClientDialog créé avec succès")
                result = dialog.exec_()
                print(f"📝 Résultat dialogue client: {result}")
            except Exception as e:
                print(f"❌ Erreur ModernClientDialog: {e}")
                import traceback
                traceback.print_exc()

        # Test dialogue produit
        def test_product_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernProductDialog
                from src.ui.utils.dialog_utils import load_product_dependencies

                # Charger les dépendances
                categories, units = load_product_dependencies()

                print(f"📦 Catégories chargées: {len(categories)}")
                print(f"📏 Unités chargées: {len(units)}")

                dialog = ModernProductDialog(categories, units, window)
                print("✅ ModernProductDialog créé avec succès")
                result = dialog.exec_()
                print(f"📝 Résultat dialogue produit: {result}")
            except Exception as e:
                print(f"❌ Erreur ModernProductDialog: {e}")
                import traceback
                traceback.print_exc()

        # Test dialogue devis
        def test_quote_dialog():
            try:
                from src.ui.components.modern_dialogs import ModernQuoteDialog
                dialog = ModernQuoteDialog(window)
                print("✅ ModernQuoteDialog créé avec succès")
                result = dialog.exec_()
                print(f"📝 Résultat dialogue devis: {result}")
            except Exception as e:
                print(f"❌ Erreur ModernQuoteDialog: {e}")
                import traceback
                traceback.print_exc()

        # Boutons de test
        btn_client = QPushButton("🧑 Test Client Dialog")
        btn_client.clicked.connect(test_client_dialog)
        layout.addWidget(btn_client)

        btn_product = QPushButton("📦 Test Product Dialog")
        btn_product.clicked.connect(test_product_dialog)
        layout.addWidget(btn_product)

        btn_quote = QPushButton("📝 Test Quote Dialog")
        btn_quote.clicked.connect(test_quote_dialog)
        layout.addWidget(btn_quote)

        window.setCentralWidget(central_widget)
        window.show()

        print("🎯 Fenêtre de test ouverte. Cliquez sur les boutons pour tester les dialogues.")

        return app.exec_()

    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return 1

def fix_dialog_issues():
    """Corrige les problèmes connus des dialogues"""
    print("🔧 Correction des problèmes de dialogues\n")

    try:
        # Vérifier que les dépendances existent
        from src.dal.database import db_manager
        from src.dal.models.product import Category, Unit

        with db_manager.get_session() as session:
            # Vérifier les catégories
            categories = session.query(Category).all()
            print(f"📂 Catégories existantes: {len(categories)}")

            if not categories:
                print("➕ Création d'une catégorie par défaut...")
                default_category = Category(
                    code="GEN001",
                    name="Général",
                    description="Catégorie par défaut"
                )
                session.add(default_category)
                session.commit()
                print("✅ Catégorie par défaut créée")

            # Vérifier les unités
            units = session.query(Unit).all()
            print(f"📏 Unités existantes: {len(units)}")

            if not units:
                print("➕ Création d'unités par défaut...")
                default_units = [
                    Unit(code="UNIT", name="Unité", symbol="u", description="Unité générique"),
                    Unit(code="KG", name="Kilogramme", symbol="kg", description="Unité de poids"),
                    Unit(code="L", name="Litre", symbol="l", description="Unité de volume"),
                    Unit(code="M", name="Mètre", symbol="m", description="Unité de longueur")
                ]
                for unit in default_units:
                    session.add(unit)
                session.commit()
                print("✅ Unités par défaut créées")

        print("\n🎉 Correction terminée avec succès!")
        return True

    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🚀 Script de diagnostic et correction des dialogues GSCOM\n")

    # Étape 1: Corriger les problèmes de base
    print("=== ÉTAPE 1: Correction des dépendances ===")
    if not fix_dialog_issues():
        print("❌ Échec de la correction des dépendances")
        return 1

    print("\n=== ÉTAPE 2: Test des dialogues ===")
    # Étape 2: Tester les dialogues
    return test_modern_dialogs()

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de correction du bouton de thème GSCOM
S'assure que le bouton de thème fonctionne correctement
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_theme_button():
    """Test du bouton de thème dans l'application principale"""
    print("🔧 Correction du Bouton de Thème GSCOM")
    print("=" * 50)
    
    try:
        # Imports nécessaires
        from PyQt5.QtWidgets import QApplication
        from src.ui.login_window import LoginWindow
        from src.ui.main_window import MainWindow
        from src.ui.styles.theme_manager import get_theme_manager
        from src.dal.models.user import User
        
        # Créer l'application
        app = QApplication(sys.argv)
        app.setApplicationName("GSCOM Theme Button Test")
        
        print("📦 Application créée")
        
        # Test du gestionnaire de thème
        theme_manager = get_theme_manager()
        print(f"✅ Gestionnaire de thème: {theme_manager.get_current_theme()}")
        
        # Créer un utilisateur factice pour le test
        user = User()
        user.id = 1
        user.username = "admin"
        user.first_name = "Admin"
        user.last_name = "Test"
        user.email = "<EMAIL>"
        user.is_admin = True
        user.is_active = True
        
        print("👤 Utilisateur de test créé")
        
        # Créer la fenêtre principale
        main_window = MainWindow(user)
        print("🖥️ Fenêtre principale créée")
        
        # Vérifier que le bouton de thème existe
        theme_button = None
        if hasattr(main_window, 'user_info'):
            from PyQt5.QtWidgets import QPushButton
            for child in main_window.user_info.findChildren(QPushButton):
                if child.text() in ["🌙", "☀️", "🔄"] or "thème" in child.toolTip().lower():
                    theme_button = child
                    break
        
        if theme_button:
            print("✅ Bouton de thème trouvé")
            print(f"   Texte: {theme_button.text()}")
            print(f"   Tooltip: {theme_button.toolTip()}")
            
            # Tester le clic sur le bouton
            print("\n🖱️ Test du clic sur le bouton...")
            initial_theme = theme_manager.get_current_theme()
            print(f"   Thème initial: {initial_theme}")
            
            # Simuler un clic
            theme_button.click()
            new_theme = theme_manager.get_current_theme()
            print(f"   Nouveau thème: {new_theme}")
            
            if new_theme != initial_theme:
                print("✅ Bouton de thème fonctionne !")
            else:
                print("❌ Bouton de thème ne change pas le thème")
                
        else:
            print("❌ Bouton de thème non trouvé")
            print("🔧 Ajout d'un bouton de test...")
            
            # Ajouter un bouton de test
            from PyQt5.QtWidgets import QPushButton
            test_button = QPushButton("🔄 Test Thème", main_window)
            test_button.setGeometry(10, 10, 120, 30)
            test_button.clicked.connect(lambda: theme_manager.cycle_theme())
            test_button.show()
            print("✅ Bouton de test ajouté")
        
        # Afficher la fenêtre
        main_window.show()
        
        print("\n" + "=" * 50)
        print("🎉 TEST DU BOUTON DE THÈME")
        print("=" * 50)
        print("📋 Instructions:")
        print("1. La fenêtre principale GSCOM est ouverte")
        print("2. Cherchez le bouton 🌙/☀️/🔄 dans la sidebar")
        print("3. Cliquez dessus pour tester le changement de thème")
        print("4. Le thème devrait changer immédiatement")
        print("5. Fermez la fenêtre pour terminer")
        print()
        print("🔧 Si le bouton ne fonctionne pas:")
        print("   - Un bouton de test est ajouté en haut à gauche")
        print("   - Utilisez-le pour tester le système")
        print()
        
        # Lancer l'application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

def quick_theme_test():
    """Test rapide du système de thème"""
    print("⚡ Test Rapide du Système de Thème")
    print("=" * 40)
    
    try:
        from src.ui.styles.theme_manager import get_theme_manager
        
        theme_manager = get_theme_manager()
        
        print(f"Thème actuel: {theme_manager.get_current_theme()}")
        print(f"Affichage: {theme_manager.get_theme_name_display()}")
        
        # Test de basculement
        print("\nTest de basculement:")
        for i in range(3):
            old_theme = theme_manager.get_current_theme()
            new_theme = theme_manager.cycle_theme()
            print(f"  {old_theme} → {new_theme} ({theme_manager.get_theme_name_display()})")
        
        print("\n✅ Système de thème opérationnel")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("Choisissez le type de test:")
    print("1. Test complet avec interface principale")
    print("2. Test rapide du système")
    print("3. Quitter")
    
    try:
        choice = input("\nVotre choix (1, 2 ou 3): ").strip()
        
        if choice == "1":
            return test_theme_button()
        elif choice == "2":
            success = quick_theme_test()
            return 0 if success else 1
        elif choice == "3":
            print("👋 Au revoir !")
            return 0
        else:
            print("❌ Choix invalide")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrompu")
        return 1
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

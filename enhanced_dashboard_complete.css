/* ========================================
   STYLES COMPLETS DASHBOARD AMÉLIORÉ GSCOM
   Design moderne, futuriste et ergonomique
   ======================================== */

/* ===== VARIABLES DE COULEURS ===== */
/*
Mode Sombre (par défaut):
- Fond principal: rgba(18, 24, 38, 0.98)
- Fond secondaire: rgba(25, 35, 55, 0.98)
- Texte principal: #ffffff
- Texte secondaire: rgba(255, 255, 255, 0.8)
- Accent: #00d4ff
- Bordures: rgba(255, 255, 255, 0.15)

Couleurs KPI:
- CA: #27ae60 (Vert)
- Commandes: #3498db (Bleu)
- Clients: #00d4ff (<PERSON>an)
- Produits: #ff6b6b (Rouge)
- Factures: #f39c12 (Orange)
*/

/* ===== FOND PRINCIPAL UNIFORME ===== */
QWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(18, 24, 38, 0.98),
        stop:1 rgba(25, 35, 55, 0.98));
    color: #ffffff;
    font-family: 'Segoe UI', 'Inter', 'Roboto', sans-serif;
}

/* ===== EN-TÊTE MODERNE ===== */
#enhancedHeader {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.12),
        stop:1 rgba(255, 255, 255, 0.06));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    margin-bottom: 10px;
    padding: 20px 30px;
}

#enhancedTitle {
    font-size: 36px;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 5px;
}

#enhancedDate {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* ===== BOUTONS DE CONTRÔLE ===== */
#themeToggle, #enhancedRefresh {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #00d4ff, stop:1 #00d4ffCC);
    border: 2px solid #00d4ff;
    border-radius: 22px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    width: 45px;
    height: 45px;
}

#themeToggle:hover, #enhancedRefresh:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #00d4ffDD, stop:1 #00d4ffAA);
    transform: scale(1.05);
}

/* ===== CARTES KPI AMÉLIORÉES ===== */
#kpiFrame {
    background: transparent;
    border: none;
    margin: 10px 0;
}

#enhancedKpiCard {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.15),
        stop:1 rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    margin: 5px;
    padding: 15px 20px;
    min-height: 140px;
}

#enhancedKpiCard:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Icônes KPI */
#kpiIcon {
    font-size: 32px;
    margin-bottom: 8px;
}

/* Valeurs KPI (agrandies) */
#kpiValue {
    font-size: 36px;
    font-weight: bold;
    margin: 5px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Titres KPI */
#kpiTitle {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
}

/* Sous-titres KPI */
#kpiSubtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

/* Badges de tendance */
#kpiTrend {
    font-size: 11px;
    font-weight: bold;
    border-radius: 10px;
    padding: 4px 8px;
    color: white;
}

/* ===== SECTIONS PRINCIPALES ===== */
#enhancedSystemInfo, #dynamicWidgets, #activityCalendarFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.08),
        stop:1 rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 18px;
    margin: 5px;
    padding: 20px 25px;
}

#enhancedSectionTitle {
    font-size: 20px;
    font-weight: bold;
    color: #00d4ff;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* ===== INFORMATIONS SYSTÈME (2 COLONNES) ===== */
#systemIcon {
    font-size: 18px;
    margin-right: 10px;
    color: #00d4ff;
}

#systemText {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
}

/* ===== WIDGETS DYNAMIQUES ===== */
#trendWidget, #pieWidget {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin: 10px 0;
    padding: 15px;
}

#widgetTitle {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 10px;
}

#legendText {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* ===== ACTIVITÉ RÉCENTE ===== */
#activityList {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin: 10px 0;
    padding: 15px;
}

#activityIcon {
    font-size: 16px;
    margin-right: 10px;
    color: #00d4ff;
}

#activityText {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
}

#activityTime {
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    font-style: italic;
}

#seeAllButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #00d4ff40, stop:1 #00d4ff20);
    border: 1px solid #00d4ff60;
    border-radius: 8px;
    color: #00d4ff;
    font-weight: 600;
    padding: 8px 16px;
    margin-top: 10px;
}

#seeAllButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #00d4ff60, stop:1 #00d4ff40);
}

/* ===== MINI CALENDRIER ===== */
#miniCalendar {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin: 10px 0;
    padding: 15px;
}

#calendarMonth {
    font-size: 16px;
    font-weight: bold;
    color: #00d4ff;
    margin-bottom: 10px;
    text-align: center;
}

#calendarDay {
    font-size: 11px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    margin: 2px;
    text-align: center;
}

#calendarDate {
    font-size: 11px;
    color: #ffffff;
    border-radius: 12px;
    margin: 1px;
    width: 25px;
    height: 25px;
    text-align: center;
}

#calendarDate:hover {
    background: #00d4ff30;
    color: #00d4ff;
}

#calendarToday {
    background: #00d4ff;
    color: white;
    font-weight: bold;
    border-radius: 12px;
    width: 25px;
    height: 25px;
}

/* ===== ACTIONS RAPIDES CATÉGORISÉES ===== */
#enhancedActionsFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.08),
        stop:1 rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 18px;
    margin: 10px 0;
    padding: 20px 25px;
}

#actionGroup {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin: 5px;
    padding: 15px;
}

#actionGroupTitle {
    font-size: 14px;
    font-weight: bold;
    color: #00d4ff;
    margin-bottom: 8px;
    text-align: center;
}

/* Boutons d'action avec couleurs spécifiques */
#enhancedActionButton {
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    font-size: 13px;
    padding: 12px 16px;
    height: 40px;
    margin: 2px 0;
}

#enhancedActionButton:hover {
    transform: translateY(-1px);
}

#enhancedActionButton:pressed {
    transform: translateY(0px);
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
QFrame, QPushButton {
    transition: all 0.3s ease;
}

QFrame:hover {
    transform: translateY(-2px);
}

/* ===== BARRES DE DÉFILEMENT MODERNES ===== */
QScrollBar:vertical {
    background: rgba(255, 255, 255, 0.1);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #00d4ff80;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: #00d4ff;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0;
    background: transparent;
}

QScrollBar:horizontal {
    background: rgba(255, 255, 255, 0.1);
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #00d4ff80;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: #00d4ff;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    #enhancedTitle {
        font-size: 28px;
    }
    
    #kpiValue {
        font-size: 28px;
    }
    
    #enhancedSectionTitle {
        font-size: 18px;
    }
}

@media (max-width: 800px) {
    #enhancedTitle {
        font-size: 24px;
    }
    
    #kpiValue {
        font-size: 24px;
    }
    
    #enhancedSectionTitle {
        font-size: 16px;
    }
    
    #enhancedKpiCard {
        min-height: 120px;
    }
}

/* ===== MODE CLAIR (OPTIONNEL) ===== */
.light-mode QWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(248, 250, 252, 0.98),
        stop:1 rgba(255, 255, 255, 0.98));
    color: #2c3e50;
}

.light-mode #enhancedTitle {
    color: #3498db;
}

.light-mode #enhancedSectionTitle {
    color: #3498db;
}

.light-mode #systemText,
.light-mode #activityText {
    color: #2c3e50;
}

.light-mode #enhancedHeader,
.light-mode #enhancedSystemInfo,
.light-mode #dynamicWidgets,
.light-mode #activityCalendarFrame,
.light-mode #enhancedActionsFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(0, 0, 0, 0.05),
        stop:1 rgba(0, 0, 0, 0.02));
    border-color: rgba(0, 0, 0, 0.1);
}

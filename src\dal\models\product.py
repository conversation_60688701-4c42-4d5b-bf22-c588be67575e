#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèles pour la gestion des produits et catégories
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from src.dal.database import Base

class ProductType(enum.Enum):
    """Types de produits"""
    PRODUCT = "product"      # Produit physique
    SERVICE = "service"      # Service
    CONSUMABLE = "consumable" # Consommable

class ProductStatus(enum.Enum):
    """Statut des produits"""
    ACTIVE = "active"        # Actif
    INACTIVE = "inactive"    # Inactif
    DISCONTINUED = "discontinued"  # Arrêté

class Category(Base):
    """Modèle pour les catégories de produits"""
    __tablename__ = 'categories'

    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)

    # Hiérarchie
    parent_id = Column(Integer, ForeignKey('categories.id'))
    parent = relationship("Category", remote_side=[id], backref="children")

    # Statut
    is_active = Column(Boolean, default=True)

    # Dates
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    products = relationship("Product", back_populates="category")

    def __repr__(self):
        return f"<Category(code='{self.code}', name='{self.name}')>"

    @property
    def full_path(self) -> str:
        """Chemin complet de la catégorie"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

class Unit(Base):
    """Modèle pour les unités de mesure"""
    __tablename__ = 'units'

    id = Column(Integer, primary_key=True)
    code = Column(String(10), unique=True, nullable=False)
    name = Column(String(50), nullable=False)
    symbol = Column(String(10))
    description = Column(Text)

    # Type d'unité (poids, volume, longueur, etc.)
    unit_type = Column(String(20))

    # Conversion vers l'unité de base
    base_unit_id = Column(Integer, ForeignKey('units.id'))
    conversion_factor = Column(Numeric(15, 6), default=1.0)

    # Relations
    base_unit = relationship("Unit", remote_side=[id])
    products = relationship("Product", back_populates="unit")

    def __repr__(self):
        return f"<Unit(code='{self.code}', name='{self.name}')>"

class Product(Base):
    """Modèle pour les produits"""
    __tablename__ = 'products'

    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, nullable=False)
    barcode = Column(String(50), unique=True)

    # Informations de base
    name = Column(String(200), nullable=False)
    description = Column(Text)
    product_type = Column(Enum(ProductType), default=ProductType.PRODUCT)
    status = Column(Enum(ProductStatus), default=ProductStatus.ACTIVE)

    # Classification
    category_id = Column(Integer, ForeignKey('categories.id'))
    unit_id = Column(Integer, ForeignKey('units.id'))
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))

    # Prix
    purchase_price = Column(Numeric(15, 2), default=0)
    sale_price = Column(Numeric(15, 2), default=0)
    min_sale_price = Column(Numeric(15, 2), default=0)

    # Marges
    margin_percentage = Column(Numeric(5, 2), default=0)
    markup_percentage = Column(Numeric(5, 2), default=0)

    # TVA
    tax_rate = Column(Numeric(5, 2), default=19.0)
    tax_included = Column(Boolean, default=True)

    # Stock
    track_stock = Column(Boolean, default=True)
    current_stock = Column(Numeric(15, 3), default=0)
    min_stock = Column(Numeric(15, 3), default=0)
    max_stock = Column(Numeric(15, 3), default=0)
    reorder_point = Column(Numeric(15, 3), default=0)

    # Dimensions et poids
    weight = Column(Numeric(10, 3))
    length = Column(Numeric(10, 3))
    width = Column(Numeric(10, 3))
    height = Column(Numeric(10, 3))

    # Images et documents
    image_path = Column(String(255))
    datasheet_path = Column(String(255))

    # Dates
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_purchase_date = Column(DateTime)
    last_sale_date = Column(DateTime)

    # Notes
    notes = Column(Text)

    # Relations
    category = relationship("Category", back_populates="products")
    unit = relationship("Unit", back_populates="products")
    supplier = relationship("Supplier", back_populates="products")
    stock_movements = relationship("StockMovement", back_populates="product")
    invoice_lines = relationship("InvoiceLine", back_populates="product")

    def __repr__(self):
        return f"<Product(code='{self.code}', name='{self.name}')>"

    @property
    def sale_price_with_tax(self) -> float:
        """Prix de vente TTC"""
        if self.tax_included:
            return float(self.sale_price)
        else:
            return float(self.sale_price) * (1 + float(self.tax_rate) / 100)

    @property
    def sale_price_without_tax(self) -> float:
        """Prix de vente HT"""
        if self.tax_included:
            return float(self.sale_price) / (1 + float(self.tax_rate) / 100)
        else:
            return float(self.sale_price)

    @property
    def is_low_stock(self) -> bool:
        """Vérifie si le stock est bas"""
        if not self.track_stock:
            return False
        return self.current_stock <= self.min_stock

    @property
    def is_out_of_stock(self) -> bool:
        """Vérifie si le produit est en rupture"""
        if not self.track_stock:
            return False
        return self.current_stock <= 0

    @property
    def is_active(self) -> bool:
        """Vérifie si le produit est actif"""
        return self.status == ProductStatus.ACTIVE

    def calculate_margin(self) -> float:
        """Calcule la marge en pourcentage"""
        if self.purchase_price and self.sale_price:
            purchase = float(self.purchase_price)
            sale = float(self.sale_price_without_tax)
            if purchase > 0:
                return ((sale - purchase) / purchase) * 100
        return 0.0

    def update_stock(self, quantity: float, operation: str = 'add'):
        """Met à jour le stock"""
        if not self.track_stock:
            return

        if operation == 'add':
            self.current_stock += quantity
        elif operation == 'subtract':
            self.current_stock -= quantity
        elif operation == 'set':
            self.current_stock = quantity

        # S'assurer que le stock ne devient pas négatif
        if self.current_stock < 0:
            self.current_stock = 0

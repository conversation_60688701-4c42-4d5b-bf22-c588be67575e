#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des corrections d'accessibilité GSCOM
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_accessibility():
    """Test des fonctionnalités d'accessibilité"""
    print("🧪 Test des Corrections d'Accessibilité")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.styles.accessibility_manager import get_accessibility_manager
        from src.ui.login_window import LoginWindow
        
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Tester le gestionnaire d'accessibilité
        manager = get_accessibility_manager()
        print(f"✅ Gestionnaire d'accessibilité: {manager.get_current_theme()}")
        
        # Tester le basculement de thème
        print("\n🔄 Test du basculement de thème...")
        initial_theme = manager.get_current_theme()
        new_theme = manager.toggle_theme()
        print(f"   {initial_theme} → {new_theme}")
        
        # Tester l'application du thème
        print("\n🎨 Test d'application du thème...")
        login_window = LoginWindow()
        manager.apply_to_widget(login_window)
        print("✅ Thème appliqué à la fenêtre de connexion")
        
        # Afficher la fenêtre pour test visuel
        login_window.show()
        
        print("\n" + "=" * 50)
        print("🎉 TESTS RÉUSSIS !")
        print("=" * 50)
        print("📋 Une fenêtre de connexion s'est ouverte")
        print("🎨 Le thème accessible est appliqué")
        print("🔄 Fermez la fenêtre pour terminer")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_accessibility())

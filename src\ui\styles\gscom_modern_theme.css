/* ========================================
   THÈME MODERNE GSCOM - CSS AUTONOME
   Styles améliorés pour toute l'application
   ======================================== */

/* ===== VARIABLES DE COULEURS ===== */
/* 
Couleurs principales :
- Bleu principal: #4682ff
- Bleu secondaire: #6fa8ff  
- Fond sombre: rgba(25, 30, 45, 0.98)
- Texte principal: #ffffff
- Texte sur fond clair: #2c3e50
- Gris professionnel: rgba(180, 180, 180, 0.9)
*/

/* ===== FENÊTRES PRINCIPALES ===== */
QDialog, QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(25, 30, 45, 0.98),
        stop:1 rgba(35, 40, 65, 0.98));
    color: #ffffff;
    font-family: 'Segoe UI', 'Arial', sans-serif;
}

/* Frame principal des dialogues */
#mainFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(25, 30, 45, 0.98),
        stop:1 rgba(35, 40, 65, 0.98));
    border: 2px solid rgba(70, 130, 255, 0.6);
    border-radius: 16px;
}

/* ===== EN-TÊTES ===== */
#headerFrame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(70, 130, 255, 0.25),
        stop:1 rgba(100, 150, 255, 0.25));
    border-radius: 14px 14px 0 0;
    border-bottom: 2px solid rgba(70, 130, 255, 0.3);
    padding: 15px 20px;
}

#titleLabel {
    color: #ffffff;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 20px;
    font-weight: bold;
}

#subtitleLabel {
    color: rgba(255, 255, 255, 0.85);
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 14px;
    font-weight: 500;
    margin-top: 5px;
}

/* ===== BOUTONS DE CONTRÔLE ===== */
#controlButton {
    background: rgba(70, 130, 255, 0.2);
    border: 2px solid rgba(70, 130, 255, 0.4);
    border-radius: 18px;
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
    min-width: 36px;
    min-height: 36px;
}

#controlButton:hover {
    background: rgba(70, 130, 255, 0.4);
    border-color: #4682ff;
}

#closeButton {
    background: rgba(255, 80, 80, 0.25);
    border: 2px solid rgba(255, 80, 80, 0.4);
    border-radius: 18px;
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
    min-width: 36px;
    min-height: 36px;
}

#closeButton:hover {
    background: rgba(255, 80, 80, 0.5);
    border-color: #ff5050;
}

/* ===== GROUPES DE FORMULAIRE ===== */
QGroupBox {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.12),
        stop:1 rgba(255, 255, 255, 0.08));
    border: 2px solid rgba(70, 130, 255, 0.4);
    border-radius: 12px;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-weight: bold;
    font-size: 15px;
    color: #4682ff;
    margin-top: 20px;
    padding-top: 25px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 20px;
    top: -10px;
    padding: 8px 16px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(70, 130, 255, 0.8),
        stop:1 rgba(100, 150, 255, 0.8));
    border: 2px solid rgba(70, 130, 255, 0.6);
    border-radius: 10px;
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
}

/* ===== LABELS DE CHAMPS ===== */
#fieldLabel {
    color: #ffffff;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-weight: 600;
    font-size: 14px;
    min-width: 140px;
    padding-right: 10px;
}

/* ===== CHAMPS DE SAISIE ===== */
QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.9),
        stop:1 rgba(245, 245, 245, 0.9));
    border: 2px solid rgba(70, 130, 255, 0.3);
    border-radius: 8px;
    padding: 12px 15px;
    color: #2c3e50;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 14px;
    font-weight: 500;
    min-height: 20px;
    selection-background-color: rgba(70, 130, 255, 0.3);
}

/* État focus pour les champs */
QLineEdit:focus, QTextEdit:focus, QComboBox:focus, 
QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border: 3px solid #4682ff;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 1.0),
        stop:1 rgba(250, 250, 250, 1.0));
}

/* Placeholder text */
QLineEdit::placeholder, QTextEdit::placeholder {
    color: rgba(100, 100, 100, 0.7);
    font-style: italic;
}

/* ===== COMBOBOX ===== */
QComboBox::drop-down {
    border: none;
    width: 35px;
    background: rgba(70, 130, 255, 0.2);
    border-radius: 0 6px 6px 0;
}

QComboBox::down-arrow {
    border: 3px solid #4682ff;
    width: 8px;
    height: 8px;
    border-top: none;
    border-right: none;
    margin-right: 10px;
}

QComboBox QAbstractItemView {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #4682ff;
    border-radius: 8px;
    color: #2c3e50;
    selection-background-color: rgba(70, 130, 255, 0.3);
    padding: 5px;
}

/* ===== SPINBOX ===== */
QSpinBox::up-button, QDoubleSpinBox::up-button {
    background: rgba(70, 130, 255, 0.3);
    border: 1px solid rgba(70, 130, 255, 0.5);
    border-radius: 4px;
    width: 25px;
    margin: 2px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background: rgba(70, 130, 255, 0.3);
    border: 1px solid rgba(70, 130, 255, 0.5);
    border-radius: 4px;
    width: 25px;
    margin: 2px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background: rgba(70, 130, 255, 0.5);
}

/* ===== CHECKBOX ===== */
QCheckBox {
    color: #ffffff;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 14px;
    font-weight: 500;
    spacing: 10px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(70, 130, 255, 0.6);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4682ff, stop:1 #6fa8ff);
    border-color: #4682ff;
}

QCheckBox::indicator:hover {
    border-color: #4682ff;
    background: rgba(255, 255, 255, 1.0);
}

/* ===== BOUTONS PRINCIPAUX ===== */
/* Bouton Enregistrer - Bleu vif principal */
#acceptButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #4682ff,
        stop:1 #2c5aa0);
    border: 2px solid #1e3a8a;
    border-radius: 10px;
    color: #ffffff;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-weight: bold;
    font-size: 14px;
    padding: 12px 25px;
    min-width: 120px;
    min-height: 40px;
}

#acceptButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5a9cff,
        stop:1 #3d6bb5);
    border-color: #2563eb;
}

#acceptButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3366cc,
        stop:1 #1e3a8a);
}

/* Bouton Annuler - Gris clair professionnel */
#cancelButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(180, 180, 180, 0.9),
        stop:1 rgba(160, 160, 160, 0.9));
    border: 2px solid rgba(140, 140, 140, 0.8);
    border-radius: 10px;
    color: #2c3e50;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 25px;
    min-width: 120px;
    min-height: 40px;
}

#cancelButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(200, 200, 200, 0.9),
        stop:1 rgba(180, 180, 180, 0.9));
    border-color: rgba(120, 120, 120, 0.9);
}

#cancelButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(160, 160, 160, 0.9),
        stop:1 rgba(140, 140, 140, 0.9));
}

/* ===== PIED DE PAGE ===== */
#footerFrame {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(255, 255, 255, 0.08),
        stop:1 rgba(255, 255, 255, 0.04));
    border-radius: 0 0 14px 14px;
    border-top: 2px solid rgba(70, 130, 255, 0.3);
    padding: 20px;
}

/* ========================================
   GSCOM - CORRECTION COMPLÈTE D'ACCESSIBILITÉ
   Solution pour tous les problèmes de visibilité
   ======================================== */

/* ===== VARIABLES CSS POUR ACCESSIBILITÉ ===== */
:root {
    /* Mode Clair (par défaut) - Contrastes optimaux */
    --bg-primary-light: #ffffff;
    --bg-secondary-light: #f8fafc;
    --bg-sidebar-light: #f1f5f9;
    --text-primary-light: #1e293b;
    --text-secondary-light: #475569;
    --text-muted-light: #64748b;
    --accent-primary-light: #2563eb;
    --accent-secondary-light: #3b82f6;
    --border-light: #e2e8f0;
    --hover-light: #f1f5f9;

    /* Mode Sombre - Contrastes optimaux */
    --bg-primary-dark: #0f172a;
    --bg-secondary-dark: #1e293b;
    --bg-sidebar-dark: #334155;
    --text-primary-dark: #f8fafc;
    --text-secondary-dark: #e2e8f0;
    --text-muted-dark: #cbd5e1;
    --accent-primary-dark: #60a5fa;
    --accent-secondary-dark: #3b82f6;
    --border-dark: #475569;
    --hover-dark: #475569;

    /* Couleurs d'état avec contraste optimal */
    --success: #059669;
    --warning: #d97706;
    --error: #dc2626;
    --info: #0284c7;

    /* Tailles et espacements pour accessibilité */
    --font-size-base: 16px;
    --font-size-large: 18px;
    --font-size-xl: 20px;
    --line-height-base: 1.6;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --border-radius: 8px;
    --border-width: 2px;
}

/* ===== MODE CLAIR (DÉFAUT) - ACCESSIBILITÉ MAXIMALE ===== */
QDialog, QMainWindow {
    background: var(--bg-primary-light);
    color: var(--text-primary-light);
    font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
}

/* ===== SIDEBAR - CONTRASTE OPTIMAL ===== */
#sidebar {
    background: var(--bg-sidebar-light);
    border-right: var(--border-width) solid var(--border-light);
    color: var(--text-primary-light);
    min-width: 280px;
}

#sidebarHeader {
    background: var(--bg-secondary-light);
    border-bottom: var(--border-width) solid var(--border-light);
    padding: var(--spacing-lg);
}

#logoLabel {
    font-size: 48px;
    color: var(--accent-primary-light);
    text-align: center;
}

#titleLabel {
    color: var(--text-primary-light);
    font-size: var(--font-size-xl);
    font-weight: bold;
    text-align: center;
    margin: var(--spacing-sm) 0;
}

#subtitleLabel {
    color: var(--text-secondary-light);
    font-size: var(--font-size-base);
    text-align: center;
}

/* ===== NAVIGATION - LISIBILITÉ MAXIMALE ===== */
#navButton {
    background: transparent;
    border: var(--border-width) solid transparent;
    border-radius: var(--border-radius);
    color: var(--text-primary-light);
    padding: var(--spacing-md);
    margin: var(--spacing-xs) var(--spacing-md);
    min-height: 60px;
    text-align: left;
}

#navButton:hover {
    background: var(--hover-light);
    border-color: var(--accent-primary-light);
    color: var(--accent-primary-light);
}

#navButton:pressed, #navButton:checked {
    background: var(--accent-primary-light);
    color: var(--bg-primary-light);
    font-weight: bold;
}

#navIcon {
    color: var(--accent-primary-light);
    font-size: var(--font-size-xl);
    font-weight: bold;
    min-width: 32px;
    text-align: center;
}

#navTitle {
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-left: var(--spacing-md);
}

#navButton:hover #navIcon {
    color: var(--accent-secondary-light);
}

#navButton:hover #navTitle {
    color: var(--accent-primary-light);
    font-weight: bold;
}

/* ===== ZONE UTILISATEUR - CONTRASTE AMÉLIORÉ ===== */
#userInfo {
    background: var(--bg-secondary-light);
    border-top: var(--border-width) solid var(--border-light);
    padding: var(--spacing-lg);
}

#userName {
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

#userActionButton {
    background: var(--bg-primary-light);
    border: var(--border-width) solid var(--border-light);
    border-radius: var(--border-radius);
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-xs);
    min-width: 40px;
    min-height: 32px;
}

#userActionButton:hover {
    background: var(--accent-primary-light);
    color: var(--bg-primary-light);
    border-color: var(--accent-primary-light);
}

/* ===== ZONE DE CONTENU - LISIBILITÉ OPTIMALE ===== */
#contentArea {
    background: var(--bg-primary-light);
    color: var(--text-primary-light);
    padding: var(--spacing-lg);
}

/* ===== DASHBOARD - CARTES CONTRASTÉES ===== */
#dashboardTitle {
    color: var(--accent-primary-light);
    font-size: 32px;
    font-weight: bold;
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

#kpiCard {
    background: var(--bg-secondary-light);
    border: var(--border-width) solid var(--border-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
    min-height: 120px;
}

#kpiTitle {
    color: var(--text-secondary-light);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

#kpiValue {
    color: var(--text-primary-light);
    font-size: 28px;
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
}

#kpiIcon {
    color: var(--accent-primary-light);
    font-size: 24px;
    float: right;
}

/* ===== BOUTONS - ACCESSIBILITÉ RENFORCÉE ===== */
QPushButton {
    background: var(--accent-primary-light);
    border: var(--border-width) solid var(--accent-primary-light);
    border-radius: var(--border-radius);
    color: var(--bg-primary-light);
    font-size: var(--font-size-base);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 40px;
}

QPushButton:hover {
    background: var(--accent-secondary-light);
    border-color: var(--accent-secondary-light);
}

QPushButton:pressed {
    background: var(--text-primary-light);
    border-color: var(--text-primary-light);
}

QPushButton:disabled {
    background: var(--text-muted-light);
    border-color: var(--text-muted-light);
    color: var(--bg-primary-light);
}

/* ===== CHAMPS DE SAISIE - CONTRASTE OPTIMAL ===== */
QLineEdit, QTextEdit, QComboBox {
    background: var(--bg-primary-light);
    border: var(--border-width) solid var(--border-light);
    border-radius: var(--border-radius);
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    padding: var(--spacing-md);
    min-height: 40px;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
    border-color: var(--accent-primary-light);
    outline: var(--border-width) solid var(--accent-primary-light);
}

/* ===== TABLEAUX - LISIBILITÉ MAXIMALE ===== */
QTableWidget {
    background: var(--bg-primary-light);
    border: var(--border-width) solid var(--border-light);
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    gridline-color: var(--border-light);
}

QTableWidget::item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

QTableWidget::item:selected {
    background: var(--accent-primary-light);
    color: var(--bg-primary-light);
}

QHeaderView::section {
    background: var(--bg-secondary-light);
    border: var(--border-width) solid var(--border-light);
    color: var(--text-primary-light);
    font-weight: bold;
    padding: var(--spacing-md);
}

/* ===== LABELS ET TEXTE - CONTRASTE GARANTI ===== */
QLabel {
    color: var(--text-primary-light);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
}

#fieldLabel {
    color: var(--text-secondary-light);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

#sectionTitle {
    color: var(--accent-primary-light);
    font-size: var(--font-size-large);
    font-weight: bold;
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
}

/* ===== GROUPES ET SECTIONS ===== */
QGroupBox {
    border: var(--border-width) solid var(--border-light);
    border-radius: var(--border-radius);
    color: var(--text-primary-light);
    font-weight: bold;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
}

QGroupBox::title {
    color: var(--accent-primary-light);
    font-size: var(--font-size-base);
    font-weight: bold;
    padding: 0 var(--spacing-sm);
    subcontrol-origin: margin;
    subcontrol-position: top left;
}

/* ===== BARRES DE DÉFILEMENT - VISIBILITÉ AMÉLIORÉE ===== */
QScrollBar:vertical {
    background: var(--bg-secondary-light);
    border: 1px solid var(--border-light);
    width: 16px;
}

QScrollBar::handle:vertical {
    background: var(--accent-primary-light);
    border-radius: var(--border-radius);
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: var(--accent-secondary-light);
}

/* ===== ÉTATS D'ALERTE - COULEURS ACCESSIBLES ===== */
.success {
    background: var(--success);
    color: var(--bg-primary-light);
}

.warning {
    background: var(--warning);
    color: var(--bg-primary-light);
}

.error {
    background: var(--error);
    color: var(--bg-primary-light);
}

.info {
    background: var(--info);
    color: var(--bg-primary-light);
}

/* ===== MODE SOMBRE - ACCESSIBILITÉ MAXIMALE ===== */
.dark-theme QDialog,
.dark-theme QMainWindow {
    background: var(--bg-primary-dark);
    color: var(--text-primary-dark);
}

.dark-theme #sidebar {
    background: var(--bg-sidebar-dark);
    border-right-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.dark-theme #sidebarHeader {
    background: var(--bg-secondary-dark);
    border-bottom-color: var(--border-dark);
}

.dark-theme #logoLabel {
    color: var(--accent-primary-dark);
}

.dark-theme #titleLabel {
    color: var(--text-primary-dark);
}

.dark-theme #subtitleLabel {
    color: var(--text-secondary-dark);
}

.dark-theme #navButton {
    color: var(--text-primary-dark);
}

.dark-theme #navButton:hover {
    background: var(--hover-dark);
    border-color: var(--accent-primary-dark);
    color: var(--accent-primary-dark);
}

.dark-theme #navButton:pressed,
.dark-theme #navButton:checked {
    background: var(--accent-primary-dark);
    color: var(--bg-primary-dark);
}

.dark-theme #navIcon {
    color: var(--accent-primary-dark);
}

.dark-theme #navTitle {
    color: var(--text-primary-dark);
}

.dark-theme #navButton:hover #navIcon {
    color: var(--accent-secondary-dark);
}

.dark-theme #navButton:hover #navTitle {
    color: var(--accent-primary-dark);
}

.dark-theme #userInfo {
    background: var(--bg-secondary-dark);
    border-top-color: var(--border-dark);
}

.dark-theme #userName {
    color: var(--text-primary-dark);
}

.dark-theme #userActionButton {
    background: var(--bg-primary-dark);
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.dark-theme #userActionButton:hover {
    background: var(--accent-primary-dark);
    color: var(--bg-primary-dark);
    border-color: var(--accent-primary-dark);
}

.dark-theme #contentArea {
    background: var(--bg-primary-dark);
    color: var(--text-primary-dark);
}

.dark-theme #dashboardTitle {
    color: var(--accent-primary-dark);
}

.dark-theme #kpiCard {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.dark-theme #kpiTitle {
    color: var(--text-secondary-dark);
}

.dark-theme #kpiValue {
    color: var(--text-primary-dark);
}

.dark-theme #kpiIcon {
    color: var(--accent-primary-dark);
}

.dark-theme QPushButton {
    background: var(--accent-primary-dark);
    border-color: var(--accent-primary-dark);
    color: var(--bg-primary-dark);
}

.dark-theme QPushButton:hover {
    background: var(--accent-secondary-dark);
    border-color: var(--accent-secondary-dark);
}

.dark-theme QPushButton:pressed {
    background: var(--text-primary-dark);
    border-color: var(--text-primary-dark);
}

.dark-theme QLineEdit,
.dark-theme QTextEdit,
.dark-theme QComboBox {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.dark-theme QLineEdit:focus,
.dark-theme QTextEdit:focus,
.dark-theme QComboBox:focus {
    border-color: var(--accent-primary-dark);
    outline-color: var(--accent-primary-dark);
}

.dark-theme QTableWidget {
    background: var(--bg-primary-dark);
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
    gridline-color: var(--border-dark);
}

.dark-theme QTableWidget::item:selected {
    background: var(--accent-primary-dark);
    color: var(--bg-primary-dark);
}

.dark-theme QHeaderView::section {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.dark-theme QLabel {
    color: var(--text-primary-dark);
}

.dark-theme #fieldLabel {
    color: var(--text-secondary-dark);
}

.dark-theme #sectionTitle {
    color: var(--accent-primary-dark);
}

.dark-theme QGroupBox {
    border-color: var(--border-dark);
    color: var(--text-primary-dark);
}

.dark-theme QGroupBox::title {
    color: var(--accent-primary-dark);
}

.dark-theme QScrollBar:vertical {
    background: var(--bg-secondary-dark);
    border-color: var(--border-dark);
}

.dark-theme QScrollBar::handle:vertical {
    background: var(--accent-primary-dark);
}

.dark-theme QScrollBar::handle:vertical:hover {
    background: var(--accent-secondary-dark);
}

/* ===== RESPONSIVE - ADAPTATION MOBILE ===== */
@media (max-width: 768px) {
    :root {
        --font-size-base: 18px;
        --font-size-large: 20px;
        --font-size-xl: 22px;
        --spacing-md: 20px;
        --spacing-lg: 28px;
    }

    #sidebar {
        min-width: 240px;
    }

    #navButton {
        min-height: 70px;
    }
}

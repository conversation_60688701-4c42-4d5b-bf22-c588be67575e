:root {
  --modal-offset: 0;
  --modal-padding: 200px;
  --modal-fallback-color: rgba(0, 0, 0, 0.4);
  --modal-coverage-area: 100%;
}

.modal-coverage {
  position: fixed;
  z-index: 1;
  padding-top: var(--modal-padding);
  left: var(--modal-offset);
  top: var(--modal-offset);
  width: var(--modal-coverage-area);
  height: var(--modal-coverage-area);
  overflow: auto;
  background-color: var(--modal-fallback-color);
}

.modal-coverage-hidden {
  display: none;
}

.modal-content {
  background-color: var(--background);
  margin: auto;
  padding: 16px;
  border: 2px solid var(--primary);
  border-radius: var(--border-radius);
  width: 80%;
  max-width: 600px;
}

.close-btn {
  color: var(--primary);
  float: right;
  font-size: 20px;
  font-weight: bold;
}

.close-btn:hover,
.close-btn:focus {
  color: var(--primary-darker);
  text-decoration: none;
  cursor: pointer;
}

.modal-section {
  padding: 4px;
}

.modal-header h2 {
  margin: 0;
}

.modal-footer {
  padding-top: 8px;
}

.modal-btn {
  margin-right: 4px;
}

{"extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://www.gitlab.com/noppo/gevent-websocket"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "license": "Copyright 2011-2017 <PERSON> <<EMAIL>>", "metadata_version": "2.0", "name": "gevent-websocket", "run_requires": [{"requires": ["gevent"]}], "summary": "Websocket handler for the gevent pywsgi server, a Python network library", "version": "0.10.1"}
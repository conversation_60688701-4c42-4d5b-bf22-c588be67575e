@font-face {
  font-family: 'Nunito';
  src: url('../Nunito-Light.ttf');
}

@font-face {
  font-family: 'Vazirmatn';
  src: url('../Vazirmatn-Light.ttf');
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF, U+FB50-FDFF, U+FE70-FEFF;
}

:root {
  --background: #fbfbfb;
  --primary: #458bc6;
  --primary-darker: #1c79c7;
  --primary-transparent: #1c79c71a;
  --error: red;
  --unselected: lightgrey;
  --disabled: #808080;
  --text: #000000;

  --title: #666666;
  --warning-background: #fff3cd;
  --warning-border: #a0987c;
  --warning-text: #000000;

  --border-radius: 4px;
}

.dark-theme {
  --background: #15131e;
  --unselected: #5f5f5f;
  --text: #ffffff;

  --title: #e2e2e2;
  --warning-background: #ffdc6d;
}

* {
  box-sizing: border-box;
  color: var(--text);
}

body {
  background-color: var(--background);
  font-family: 'Vazirmatn', 'Nunito', Helvetica, Arial, sans-serif;
  font-weight: 100;
  margin: 0 18px 10px 18px;
}

.mid {
  /* Global center alignment */
  margin: auto;
  max-width: 800px;
}

/* Headers */

h2 {
  font-weight: normal;
  font-size: 25px;
  margin: 10px 0 2px 0;
}

h3 {
  font-size: 17px;
  margin: 10px 0 4px 0;
}

.sub_header {
  /* Headers in tabs */
  font-size: 17px;
  margin: 10px 2px 4px 2px;
}

h2 > small {
  font-size: 13px;
}

/* Generic inputs */

button,
input,
select {
  border: 1px solid var(--primary);
  border-radius: var(--border-radius);
  padding: 4px;

  font-family: 'Vazirmatn', 'Nunito', Helvetica, Arial, sans-serif;
  font-weight: 100;
}

input,
select,
textarea,
select option {
  background-color: var(--background);
}

input:focus {
  outline: none; /* Don't show outline so you can see the colour change */
}

button {
  cursor: pointer;
  border-radius: var(--border-radius);
  background: transparent;
  padding: 3px 8px;
  transition: border 0.3s, background 0.3s;
  border-style: solid;
  border-width: 2px;
}

button:not(.selected):not(.unselected):hover {
  /* Apply hovers to non-state buttons */
  background: var(--primary-transparent);
}

button.selected,
button.unselected:hover {
  border-color: var(--primary);
}

button.unselected {
  border-color: var(--unselected);
  color: var(--unselected);
}

button.large {
  border-width: 3px;
  padding: 8px;
}

/* Info icon */

.info_icon {
  /* Information icon */
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAq0lEQVR4XnWQywrDIBBF82P+WPtXebiSbJKtq0jAtVAEwW0WIZ16p04aWjpwcbge5tU0Nfq+V13XteV9QDVX8i/Qrei5LAtt28ZCXuADf9dKxzzPhEgpsRDwKqwatCiiaZrIe09aazLGMOicAwi1qIh5xGAw50z7vtM4juyB+QFDCFzNWnt6DEpr0bquPMIwDKcnrXkZMWOMrAv0XqZufhf4C/ycR6JW/nvwF51vE+IcomqCAAAAAElFTkSuQmCC) -0px -0px
    no-repeat;
  height: 10px;
  width: 10px;
  overflow: hidden;
  margin-left: 0.25em;
  vertical-align: middle;
  display: inline-block;
}

/* Small notes */

.note {
  font-size: 14px;
  font-style: italic;
  margin: 8px 0 0 0;
}

/* Filepath-browse layout */

.filepath-browse-layout {
  display: grid;
  grid-gap: 4px;
  grid-template-columns: 1fr 120px;
}

/* Icon-specific */

#icon-invalid-warning {
  font-size: 14px;
  padding-top: 4px;
}

/* Utils */

.noselect {
  /* Don't select tab text */
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'application des corrections d'accessibilité GSCOM
Corrige automatiquement tous les problèmes de visibilité
"""

import sys
import os
import shutil
from pathlib import Path

def print_banner():
    """Affiche la bannière"""
    print("=" * 60)
    print("🎨 GSCOM - Correction d'Accessibilité")
    print("=" * 60)
    print("🔧 Application des corrections de visibilité")
    print("=" * 60)
    print()

def backup_existing_files():
    """Sauvegarde les fichiers existants"""
    print("💾 Sauvegarde des fichiers existants...")
    
    backup_dir = Path("backup_accessibility")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/ui/styles/theme_manager.py",
        "src/ui/styles/unified_color_system.py",
        "src/ui/main_window.py"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✅ Sauvegardé: {file_path}")
    
    print(f"✅ Sauvegarde terminée dans: {backup_dir}")

def copy_accessibility_files():
    """Copie les nouveaux fichiers d'accessibilité"""
    print("\n📁 Copie des fichiers d'accessibilité...")
    
    # Copier le CSS d'accessibilité
    css_source = "gscom_accessibility_fix.css"
    css_dest = "src/ui/styles/gscom_accessibility_fix.css"
    
    if os.path.exists(css_source):
        os.makedirs(os.path.dirname(css_dest), exist_ok=True)
        shutil.copy2(css_source, css_dest)
        print(f"✅ CSS d'accessibilité copié: {css_dest}")
    
    # Copier le gestionnaire d'accessibilité
    manager_source = "gscom_accessibility_manager.py"
    manager_dest = "src/ui/styles/accessibility_manager.py"
    
    if os.path.exists(manager_source):
        shutil.copy2(manager_source, manager_dest)
        print(f"✅ Gestionnaire d'accessibilité copié: {manager_dest}")

def update_main_window():
    """Met à jour la fenêtre principale pour utiliser l'accessibilité"""
    print("\n🔧 Mise à jour de la fenêtre principale...")
    
    main_window_path = "src/ui/main_window.py"
    
    if not os.path.exists(main_window_path):
        print("❌ Fichier main_window.py non trouvé")
        return False
    
    # Lire le fichier existant
    with open(main_window_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter les imports d'accessibilité si pas déjà présents
    if "accessibility_manager" not in content:
        import_line = "from src.ui.styles.accessibility_manager import get_accessibility_manager, apply_accessible_theme\n"
        
        # Trouver la ligne d'import appropriée
        lines = content.split('\n')
        insert_index = 0
        
        for i, line in enumerate(lines):
            if line.startswith("from src.ui.styles"):
                insert_index = i + 1
            elif line.startswith("class MainWindow"):
                break
        
        lines.insert(insert_index, import_line)
        content = '\n'.join(lines)
    
    # Ajouter l'initialisation d'accessibilité dans __init__
    if "apply_accessible_theme" not in content:
        init_code = """
        # Appliquer le thème accessible
        apply_accessible_theme(self)
        self.accessibility_manager = get_accessibility_manager()
        self.accessibility_manager.theme_changed.connect(self.on_theme_changed)
        """
        
        # Trouver la fin de __init__
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "self.show_dashboard()" in line:
                lines.insert(i, init_code)
                break
        
        content = '\n'.join(lines)
    
    # Ajouter la méthode de gestion du changement de thème
    if "def on_theme_changed" not in content:
        method_code = """
    def on_theme_changed(self, theme_name):
        \"\"\"Gère le changement de thème\"\"\"
        self.logger.info(f"Thème changé vers: {theme_name}")
        # Mettre à jour l'interface si nécessaire
        self.update()
        """
        
        # Ajouter à la fin de la classe
        lines = content.split('\n')
        lines.append(method_code)
        content = '\n'.join(lines)
    
    # Sauvegarder le fichier modifié
    with open(main_window_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fenêtre principale mise à jour")
    return True

def create_theme_button_fix():
    """Crée un correctif pour le bouton de thème"""
    print("\n🔘 Création du correctif pour le bouton de thème...")
    
    button_fix = """
def fix_theme_button(main_window):
    \"\"\"Corrige le bouton de thème pour l'accessibilité\"\"\"
    from PyQt5.QtWidgets import QPushButton
    from src.ui.styles.accessibility_manager import get_accessibility_manager
    
    accessibility_manager = get_accessibility_manager()
    
    # Chercher le bouton de thème existant
    theme_button = None
    if hasattr(main_window, 'user_info'):
        for child in main_window.user_info.findChildren(QPushButton):
            if child.text() in ["🌙", "☀️", "🔄"] or "thème" in child.toolTip().lower():
                theme_button = child
                break
    
    if theme_button:
        # Reconnecter le bouton
        theme_button.clicked.disconnect()
        theme_button.clicked.connect(lambda: toggle_accessible_theme_with_feedback(main_window))
        
        # Mettre à jour l'apparence
        update_theme_button_appearance(theme_button, accessibility_manager.get_current_theme())
        
        print("✅ Bouton de thème corrigé")
    else:
        print("⚠️ Bouton de thème non trouvé")

def toggle_accessible_theme_with_feedback(main_window):
    \"\"\"Bascule le thème avec feedback utilisateur\"\"\"
    from src.ui.styles.accessibility_manager import get_accessibility_manager
    
    accessibility_manager = get_accessibility_manager()
    new_theme = accessibility_manager.toggle_theme()
    
    # Mettre à jour le bouton
    theme_button = None
    if hasattr(main_window, 'user_info'):
        for child in main_window.user_info.findChildren(QPushButton):
            if child.text() in ["🌙", "☀️"] or "thème" in child.toolTip().lower():
                theme_button = child
                break
    
    if theme_button:
        update_theme_button_appearance(theme_button, new_theme)
    
    # Afficher notification
    if hasattr(main_window, 'statusBar'):
        status_bar = main_window.statusBar()
        if status_bar:
            display_name = accessibility_manager.get_theme_display_name()
            status_bar.showMessage(f"Thème changé: {display_name}", 3000)

def update_theme_button_appearance(button, theme):
    \"\"\"Met à jour l'apparence du bouton de thème\"\"\"
    if theme == "dark":
        button.setText("🌙")
        button.setToolTip("Thème actuel: 🌙 Sombre\\nCliquer pour passer au mode clair")
    else:
        button.setText("☀️")
        button.setToolTip("Thème actuel: ☀️ Clair\\nCliquer pour passer au mode sombre")
"""
    
    # Sauvegarder le correctif
    with open("theme_button_fix.py", 'w', encoding='utf-8') as f:
        f.write(button_fix)
    
    print("✅ Correctif du bouton de thème créé: theme_button_fix.py")

def create_test_script():
    """Crée un script de test pour l'accessibilité"""
    print("\n🧪 Création du script de test...")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des corrections d'accessibilité GSCOM
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_accessibility():
    """Test des fonctionnalités d'accessibilité"""
    print("🧪 Test des Corrections d'Accessibilité")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.styles.accessibility_manager import get_accessibility_manager
        from src.ui.login_window import LoginWindow
        
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Tester le gestionnaire d'accessibilité
        manager = get_accessibility_manager()
        print(f"✅ Gestionnaire d'accessibilité: {manager.get_current_theme()}")
        
        # Tester le basculement de thème
        print("\\n🔄 Test du basculement de thème...")
        initial_theme = manager.get_current_theme()
        new_theme = manager.toggle_theme()
        print(f"   {initial_theme} → {new_theme}")
        
        # Tester l'application du thème
        print("\\n🎨 Test d'application du thème...")
        login_window = LoginWindow()
        manager.apply_to_widget(login_window)
        print("✅ Thème appliqué à la fenêtre de connexion")
        
        # Afficher la fenêtre pour test visuel
        login_window.show()
        
        print("\\n" + "=" * 50)
        print("🎉 TESTS RÉUSSIS !")
        print("=" * 50)
        print("📋 Une fenêtre de connexion s'est ouverte")
        print("🎨 Le thème accessible est appliqué")
        print("🔄 Fermez la fenêtre pour terminer")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_accessibility())
'''
    
    with open("test_accessibility.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Script de test créé: test_accessibility.py")

def main():
    """Fonction principale"""
    print_banner()
    
    try:
        # Étapes d'application
        backup_existing_files()
        copy_accessibility_files()
        update_main_window()
        create_theme_button_fix()
        create_test_script()
        
        print("\n" + "=" * 60)
        print("🎉 CORRECTIONS D'ACCESSIBILITÉ APPLIQUÉES !")
        print("=" * 60)
        print()
        print("✅ Fichiers CSS d'accessibilité installés")
        print("✅ Gestionnaire d'accessibilité intégré")
        print("✅ Fenêtre principale mise à jour")
        print("✅ Correctif du bouton de thème créé")
        print("✅ Script de test généré")
        print()
        print("🚀 Pour tester les corrections :")
        print("   python test_accessibility.py")
        print()
        print("🎨 Fonctionnalités disponibles :")
        print("   • Mode clair par défaut (contraste optimal)")
        print("   • Mode sombre accessible")
        print("   • Bouton de basculement fonctionnel")
        print("   • Standards WCAG 2.1 AA respectés")
        print("   • Texte et icônes parfaitement lisibles")
        print()
        print("💾 Sauvegarde dans: backup_accessibility/")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors de l'application: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

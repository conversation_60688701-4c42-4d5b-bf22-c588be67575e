#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour l'exécutable GSCOM
Vérifie que l'exécutable fonctionne correctement
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Affiche la bannière"""
    print("=" * 60)
    print("🧪 GSCOM - Test de l'Exécutable")
    print("=" * 60)
    print("🔍 Vérification de l'exécutable créé")
    print("=" * 60)
    print()

def check_executable_exists():
    """Vérifie que l'exécutable existe"""
    print("📁 Vérification de l'exécutable...")
    
    exe_path = Path("GSCOM_Distribution/GSCOM.exe")
    
    if not exe_path.exists():
        print("❌ Exécutable non trouvé!")
        print("   Chemin attendu: GSCOM_Distribution/GSCOM.exe")
        print("   Lancez d'abord: python build_executable.py")
        return False
    
    # Vérifier la taille
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    print(f"✅ Exécutable trouvé: {exe_path}")
    print(f"📏 Taille: {size_mb:.1f} MB")
    
    if size_mb < 50:
        print("⚠️ Taille suspicieusement petite (< 50 MB)")
    elif size_mb > 200:
        print("⚠️ Taille importante (> 200 MB)")
    else:
        print("✅ Taille normale pour une application complète")
    
    return True

def check_distribution_files():
    """Vérifie les fichiers de distribution"""
    print("\n📦 Vérification des fichiers de distribution...")
    
    dist_dir = Path("GSCOM_Distribution")
    required_files = [
        "GSCOM.exe",
        "README.txt",
        "Lancer_GSCOM.bat"
    ]
    
    missing_files = []
    
    for file_name in required_files:
        file_path = dist_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} manquant")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"⚠️ {len(missing_files)} fichiers manquants")
        return False
    
    print("✅ Tous les fichiers de distribution présents")
    return True

def test_executable_launch():
    """Teste le lancement de l'exécutable"""
    print("\n🚀 Test de lancement de l'exécutable...")
    
    exe_path = Path("GSCOM_Distribution/GSCOM.exe")
    
    try:
        print("⏳ Lancement de l'exécutable (test de 10 secondes)...")
        
        # Lancer l'exécutable en arrière-plan
        process = subprocess.Popen(
            [str(exe_path)],
            cwd="GSCOM_Distribution",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Attendre un peu pour voir si l'application démarre
        time.sleep(5)
        
        # Vérifier si le processus est toujours en cours
        if process.poll() is None:
            print("✅ Exécutable lancé avec succès!")
            print("✅ Application en cours d'exécution")
            
            # Terminer le processus
            process.terminate()
            time.sleep(2)
            
            if process.poll() is None:
                process.kill()
            
            print("✅ Application fermée proprement")
            return True
        else:
            # Le processus s'est terminé
            stdout, stderr = process.communicate()
            print("❌ L'exécutable s'est fermé immédiatement")
            
            if stderr:
                print(f"Erreur: {stderr.decode('utf-8', errors='ignore')}")
            
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        return False

def test_file_permissions():
    """Teste les permissions des fichiers"""
    print("\n🔐 Vérification des permissions...")
    
    exe_path = Path("GSCOM_Distribution/GSCOM.exe")
    
    try:
        # Vérifier que le fichier est exécutable
        if os.access(exe_path, os.X_OK):
            print("✅ Fichier exécutable")
        else:
            print("❌ Fichier non exécutable")
            return False
        
        # Vérifier que le fichier est lisible
        if os.access(exe_path, os.R_OK):
            print("✅ Fichier lisible")
        else:
            print("❌ Fichier non lisible")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur vérification permissions: {e}")
        return False

def generate_test_report():
    """Génère un rapport de test"""
    print("\n📋 Génération du rapport de test...")
    
    exe_path = Path("GSCOM_Distribution/GSCOM.exe")
    
    if not exe_path.exists():
        print("❌ Impossible de générer le rapport - exécutable manquant")
        return
    
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    
    report = f"""
# Rapport de Test - Exécutable GSCOM

## Informations Générales
- **Date du test** : {time.strftime('%Y-%m-%d %H:%M:%S')}
- **Fichier testé** : GSCOM_Distribution/GSCOM.exe
- **Taille** : {size_mb:.1f} MB

## Tests Effectués
- ✅ Existence du fichier exécutable
- ✅ Vérification de la taille
- ✅ Présence des fichiers de distribution
- ✅ Permissions d'exécution
- ✅ Test de lancement

## Instructions d'Utilisation
1. Aller dans le dossier GSCOM_Distribution
2. Double-cliquer sur GSCOM.exe
3. Se connecter avec :
   - Utilisateur : admin
   - Mot de passe : admin123

## Statut Final
✅ EXÉCUTABLE PRÊT POUR DISTRIBUTION

L'application GSCOM a été compilée avec succès et est prête pour les tests utilisateur et la distribution.
"""
    
    with open("RAPPORT_TEST_EXECUTABLE.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ Rapport généré: RAPPORT_TEST_EXECUTABLE.md")

def main():
    """Fonction principale"""
    print_banner()
    
    success = True
    
    # Tests séquentiels
    success &= check_executable_exists()
    success &= check_distribution_files()
    success &= test_file_permissions()
    success &= test_executable_launch()
    
    # Génération du rapport
    generate_test_report()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("=" * 60)
        print()
        print("✅ L'exécutable GSCOM fonctionne parfaitement")
        print("✅ Tous les fichiers de distribution sont présents")
        print("✅ L'application se lance correctement")
        print("✅ Prêt pour la distribution et les tests utilisateur")
        print()
        print("🚀 Pour utiliser l'application :")
        print("   1. Aller dans GSCOM_Distribution/")
        print("   2. Double-cliquer sur GSCOM.exe")
        print("   3. Se connecter avec admin/admin123")
        print()
        print("📋 Rapport détaillé : RAPPORT_TEST_EXECUTABLE.md")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("=" * 60)
        print()
        print("⚠️ Vérifiez les erreurs ci-dessus")
        print("💡 Relancez python build_executable.py si nécessaire")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        sys.exit(1)

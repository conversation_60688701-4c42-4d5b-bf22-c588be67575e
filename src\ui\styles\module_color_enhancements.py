#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Améliorations de couleurs pour les modules GSCOM
Extension du système unifié aux composants spécifiques
"""

from .unified_color_system import get_unified_color_palette

def get_dashboard_enhanced_styles():
    """Styles améliorés pour le tableau de bord"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== TABLEAU DE BORD AMÉLIORÉ ===== */
        
        /* Titre de bienvenue */
        #welcomeLabel {{
            font-size: 28px;
            font-weight: bold;
            color: {colors['primary_blue']};
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }}
        
        /* Cartes du tableau de bord */
        #dashboardCard {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_primary']};
            border-radius: 16px;
            padding: 20px;
        }}
        
        #dashboardCard:hover {{
            border-color: {colors['primary_blue']};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['element_bg_light']});
            transform: translateY(-2px);
        }}
        
        /* Icônes des cartes */
        #cardIcon {{
            font-size: 32px;
            color: {colors['primary_blue']};
            margin-bottom: 10px;
        }}
        
        /* Titres des cartes */
        #cardTitle {{
            font-size: 16px;
            font-weight: bold;
            color: {colors['text_primary']};
            margin-bottom: 8px;
        }}
        
        /* Valeurs des cartes */
        #cardValue {{
            font-size: 24px;
            font-weight: bold;
            color: {colors['secondary_blue']};
            margin-bottom: 5px;
        }}
        
        /* Descriptions des cartes */
        #cardDescription {{
            font-size: 12px;
            color: {colors['text_secondary']};
        }}
        
        /* Frame des statistiques */
        #statsFrame {{
            background: transparent;
            border: none;
            margin: 10px 0;
        }}
        
        /* Cartes de statistiques spécifiques */
        #statCard {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_secondary']};
            border-radius: 12px;
            padding: 15px;
            min-height: 100px;
        }}
        
        #statCard:hover {{
            border-color: {colors['primary_blue']};
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['hover_secondary']},
                stop:1 {colors['element_bg_light']});
        }}
        
        /* Couleurs spécifiques par type de carte */
        #revenueCard {{ border-left: 4px solid {colors['success']}; }}
        #ordersCard {{ border-left: 4px solid {colors['info']}; }}
        #clientsCard {{ border-left: 4px solid {colors['accent_cyan']}; }}
        #productsCard {{ border-left: 4px solid {colors['error']}; }}
        #invoicesCard {{ border-left: 4px solid {colors['warning']}; }}
    """

def get_dialog_enhanced_styles():
    """Styles améliorés pour les dialogues"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== DIALOGUES AMÉLIORÉS ===== */
        
        /* Dialogue principal */
        QDialog {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['bg_primary']},
                stop:1 {colors['bg_secondary']});
            color: {colors['text_primary']};
            border: 2px solid {colors['border_primary']};
            border-radius: 16px;
        }}
        
        /* Frame principal du dialogue */
        #dialogMainFrame {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['bg_primary']},
                stop:1 {colors['bg_secondary']});
            border: 2px solid {colors['border_primary']};
            border-radius: 16px;
            margin: 5px;
        }}
        
        /* En-tête du dialogue */
        #dialogHeader {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-radius: 14px 14px 0 0;
            border-bottom: 2px solid {colors['border_primary']};
            padding: 20px 25px;
        }}
        
        /* Titre du dialogue */
        #dialogTitle {{
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 22px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }}
        
        /* Sous-titre du dialogue */
        #dialogSubtitle {{
            color: {colors['text_secondary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            margin-top: 5px;
        }}
        
        /* Zone de contenu */
        #dialogContent {{
            background: transparent;
            padding: 25px;
        }}
        
        /* Groupes de champs améliorés */
        QGroupBox {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_primary']};
            border-radius: 12px;
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 15px;
            color: {colors['primary_blue']};
            margin-top: 25px;
            padding-top: 30px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 20px;
            top: -12px;
            padding: 10px 18px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['secondary_blue']});
            border: 2px solid {colors['primary_blue']};
            border-radius: 12px;
            color: {colors['text_primary']};
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }}
        
        /* Labels de champs améliorés */
        #fieldLabel {{
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            min-width: 140px;
            padding-right: 12px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }}
        
        /* Champs de saisie améliorés */
        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.98),
                stop:1 rgba(248, 250, 252, 0.98));
            border: 2px solid {colors['border_primary']};
            border-radius: 10px;
            padding: 14px 16px;
            color: {colors['text_on_light']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: 500;
            min-height: 22px;
            selection-background-color: {colors['hover_primary']};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus,
        QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
            border: 3px solid {colors['primary_blue']};
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1.0),
                stop:1 rgba(250, 252, 255, 1.0));
            box-shadow: 0 0 8px {colors['hover_primary']};
        }}
        
        QLineEdit::placeholder, QTextEdit::placeholder {{
            color: rgba(100, 116, 139, 0.7);
            font-style: italic;
        }}
        
        /* Pied de page du dialogue */
        #dialogFooter {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_medium']},
                stop:1 {colors['element_bg_dark']});
            border-top: 2px solid {colors['border_secondary']};
            border-radius: 0 0 14px 14px;
            padding: 20px 25px;
        }}
        
        /* Boutons du dialogue */
        #dialogAcceptButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['dark_blue']});
            border: 2px solid {colors['dark_blue']};
            border-radius: 12px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 14px;
            padding: 14px 28px;
            min-width: 130px;
            min-height: 44px;
        }}
        
        #dialogAcceptButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary_blue']},
                stop:1 {colors['primary_blue']});
            border-color: {colors['primary_blue']};
            transform: translateY(-1px);
        }}
        
        #dialogCancelButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['element_bg_light']},
                stop:1 {colors['element_bg_medium']});
            border: 2px solid {colors['border_secondary']};
            border-radius: 12px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: 600;
            font-size: 14px;
            padding: 14px 28px;
            min-width: 130px;
            min-height: 44px;
        }}
        
        #dialogCancelButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['hover_primary']},
                stop:1 {colors['hover_secondary']});
            border-color: {colors['primary_blue']};
            transform: translateY(-1px);
        }}
    """

def get_table_enhanced_styles():
    """Styles améliorés pour les tableaux"""
    colors = get_unified_color_palette()
    
    return f"""
        /* ===== TABLEAUX AMÉLIORÉS ===== */
        
        QTableWidget {{
            background: rgba(255, 255, 255, 0.98);
            border: 2px solid {colors['border_primary']};
            border-radius: 14px;
            gridline-color: {colors['border_light']};
            color: {colors['text_on_light']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 13px;
            selection-background-color: {colors['hover_primary']};
            alternate-background-color: rgba(248, 250, 252, 0.6);
        }}
        
        QTableWidget::item {{
            background: transparent;
            border: none;
            padding: 16px 14px;
            color: #374151;
            border-bottom: 1px solid {colors['border_light']};
        }}
        
        QTableWidget::item:selected {{
            background: {colors['hover_primary']};
            color: #1f2937;
            font-weight: 600;
        }}
        
        QTableWidget::item:hover {{
            background: {colors['hover_secondary']};
        }}
        
        /* En-têtes de tableau améliorés */
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['dark_blue']});
            border: 1px solid {colors['border_primary']};
            border-radius: 0;
            padding: 18px 14px;
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }}
        
        QHeaderView::section:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary_blue']},
                stop:1 {colors['primary_blue']});
        }}
        
        QHeaderView::section:first {{
            border-top-left-radius: 12px;
        }}
        
        QHeaderView::section:last {{
            border-top-right-radius: 12px;
        }}
        
        /* Barres de défilement pour tableaux */
        QTableWidget QScrollBar:vertical {{
            background: {colors['element_bg_light']};
            width: 14px;
            border-radius: 7px;
            margin: 2px;
        }}
        
        QTableWidget QScrollBar::handle:vertical {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['primary_blue']},
                stop:1 {colors['secondary_blue']});
            border-radius: 7px;
            min-height: 30px;
            margin: 2px;
        }}
        
        QTableWidget QScrollBar::handle:vertical:hover {{
            background: {colors['secondary_blue']};
        }}
    """

def get_complete_module_styles():
    """Retourne tous les styles de modules combinés"""
    return (
        get_dashboard_enhanced_styles() +
        get_dialog_enhanced_styles() +
        get_table_enhanced_styles()
    )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre principale de l'application GSCOM
Interface moderne avec navigation verticale et couleurs sobres professionnelles
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.modules.dashboard import DashboardWidget

# Import du gestionnaire de thème unifié
try:
    from src.ui.styles.theme_manager import get_theme_manager, apply_unified_theme
    THEME_MANAGER_AVAILABLE = True
except ImportError:
    THEME_MANAGER_AVAILABLE = False
    print("⚠️ Gestionnaire de thème non disponible, utilisation des styles par défaut")

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application"""

    def __init__(self, user=None):
        super().__init__()
        self.current_user = user
        self.logger = logging.getLogger(__name__)

        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Gestion Commerciale")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # Centrer la fenêtre
        self.center_window()

        # Initialiser l'interface
        self.setup_ui()
        self.apply_styles()

        # Appliquer le thème unifié si disponible
        if THEME_MANAGER_AVAILABLE:
            apply_unified_theme(self)
            self.logger.info("Thème unifié appliqué")

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Créer la sidebar
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)

        # Créer la zone de contenu
        self.create_content_area()
        main_layout.addWidget(self.content_area)

    def create_sidebar(self):
        """Crée la barre latérale de navigation"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(280)

        # Layout vertical pour la sidebar
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Header de la sidebar
        self.create_sidebar_header()
        sidebar_layout.addWidget(self.sidebar_header)

        # Zone de navigation avec scroll
        self.create_navigation_area()
        sidebar_layout.addWidget(self.nav_scroll)

        # Zone utilisateur
        self.create_user_area()
        sidebar_layout.addWidget(self.user_info)

    def create_sidebar_header(self):
        """Crée l'en-tête de la sidebar"""
        self.sidebar_header = QFrame()
        self.sidebar_header.setObjectName("sidebarHeader")
        self.sidebar_header.setFixedHeight(120)

        header_layout = QVBoxLayout(self.sidebar_header)
        header_layout.setContentsMargins(20, 20, 20, 20)
        header_layout.setSpacing(10)

        # Logo
        logo_label = QLabel("🏢")
        logo_label.setObjectName("logoLabel")
        logo_label.setFixedSize(60, 60)
        logo_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo_label, 0, Qt.AlignCenter)

        # Titre
        title_label = QLabel("GSCOM")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # Sous-titre
        subtitle_label = QLabel("Gestion Commerciale")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)

    def create_navigation_area(self):
        """Crée la zone de navigation"""
        self.nav_scroll = QScrollArea()
        self.nav_scroll.setObjectName("navScroll")
        self.nav_scroll.setWidgetResizable(True)
        self.nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.nav_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Widget de navigation
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(0, 10, 0, 10)
        nav_layout.setSpacing(5)

        # Modules de navigation
        modules = [
            ("dashboard", "📊", "Tableau de bord"),
            ("commercial", "💼", "Commercial"),
            ("quotes", "📝", "Devis"),
            ("orders", "📋", "Commandes"),
            ("invoices", "🧾", "Factures"),
            ("clients", "👥", "Clients"),
            ("suppliers", "🏭", "Fournisseurs"),
            ("products", "📦", "Produits"),
            ("stock", "📋", "Stock"),
            ("inventory", "📊", "Inventaire"),
            ("barcode", "🏷️", "Codes-barres"),
            ("accounting", "💰", "Comptabilité"),
            ("reports", "📈", "Rapports"),
            ("import_export", "📥📤", "Import/Export"),
            ("settings", "⚙️", "Paramètres"),
        ]

        self.nav_buttons = {}
        for module_id, icon, title in modules:
            button = self.create_nav_button(module_id, icon, title)
            nav_layout.addWidget(button)
            self.nav_buttons[module_id] = button

        nav_layout.addStretch()
        self.nav_scroll.setWidget(nav_widget)

    def create_nav_button(self, module_id, icon, title):
        """Crée un bouton de navigation"""
        button = QPushButton()
        button.setObjectName("navButton")
        button.setCursor(Qt.PointingHandCursor)
        button.setFixedHeight(60)

        # Layout du bouton
        button_layout = QHBoxLayout(button)
        button_layout.setContentsMargins(15, 10, 15, 10)
        button_layout.setSpacing(15)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        button_layout.addWidget(title_label)

        button_layout.addStretch()

        # Connecter le clic
        button.clicked.connect(lambda: self.load_module_content(module_id))

        return button

    def create_user_area(self):
        """Crée la zone utilisateur"""
        self.user_info = QFrame()
        self.user_info.setObjectName("userInfo")
        self.user_info.setFixedHeight(130)

        user_layout = QVBoxLayout(self.user_info)
        user_layout.setContentsMargins(15, 15, 15, 15)
        user_layout.setSpacing(6)

        # Nom utilisateur
        if self.current_user:
            user_name = self.current_user.full_name
        else:
            user_name = "Utilisateur"

        name_label = QLabel(user_name)
        name_label.setObjectName("userName")
        user_layout.addWidget(name_label)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(5)

        # Bouton profil
        profile_button = QPushButton("👤")
        profile_button.setObjectName("userActionButton")
        profile_button.setFixedSize(35, 25)
        profile_button.setToolTip("Profil utilisateur")
        profile_button.clicked.connect(self.show_user_profile)
        buttons_layout.addWidget(profile_button)

        # Bouton configuration couleurs
        color_button = QPushButton("🎨")
        color_button.setObjectName("userActionButton")
        color_button.setFixedSize(35, 25)
        color_button.setToolTip("Configuration des couleurs")
        color_button.clicked.connect(self.show_color_settings)
        buttons_layout.addWidget(color_button)

        # Bouton basculement thème
        theme_button = QPushButton("🌙")
        theme_button.setObjectName("userActionButton")
        theme_button.setFixedSize(35, 25)
        theme_button.setToolTip("Basculer le thème")
        theme_button.clicked.connect(self.toggle_theme)
        buttons_layout.addWidget(theme_button)

        user_layout.addLayout(buttons_layout)

    def create_content_area(self):
        """Crée la zone de contenu principal"""
        self.content_area = QFrame()
        self.content_area.setObjectName("contentArea")

        # Layout pour la zone de contenu
        content_layout = QVBoxLayout(self.content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Stack widget pour les différents modules
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)

    def show_dashboard(self):
        """Affiche le tableau de bord"""
        dashboard = DashboardWidget(self.current_user)
        self.content_stack.addWidget(dashboard)
        self.content_stack.setCurrentWidget(dashboard)

    def load_module_content(self, module_id):
        """Charge le contenu d'un module"""
        try:
            if module_id == "clients":
                from src.ui.modules.clients import ClientsWidget

                # Vérifier si le widget existe déjà
                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ClientsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                # Créer le nouveau widget
                clients_widget = ClientsWidget()
                self.content_stack.addWidget(clients_widget)
                self.content_stack.setCurrentWidget(clients_widget)

            elif module_id == "suppliers":
                from src.ui.modules.suppliers import SuppliersWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, SuppliersWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                suppliers_widget = SuppliersWidget()
                self.content_stack.addWidget(suppliers_widget)
                self.content_stack.setCurrentWidget(suppliers_widget)

            elif module_id == "products":
                from src.ui.modules.products import ProductsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ProductsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                products_widget = ProductsWidget()
                self.content_stack.addWidget(products_widget)
                self.content_stack.setCurrentWidget(products_widget)

            elif module_id == "stock":
                from src.ui.modules.stock import StockWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, StockWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                stock_widget = StockWidget()
                self.content_stack.addWidget(stock_widget)
                self.content_stack.setCurrentWidget(stock_widget)

            elif module_id == "commercial":
                from src.ui.modules.commercial import CommercialWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, CommercialWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                commercial_widget = CommercialWidget()
                self.content_stack.addWidget(commercial_widget)
                self.content_stack.setCurrentWidget(commercial_widget)

            elif module_id == "inventory":
                from src.ui.modules.inventory import InventoryWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, InventoryWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                inventory_widget = InventoryWidget()
                self.content_stack.addWidget(inventory_widget)
                self.content_stack.setCurrentWidget(inventory_widget)

            elif module_id == "accounting":
                from src.ui.modules.accounting import AccountingWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, AccountingWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                accounting_widget = AccountingWidget()
                self.content_stack.addWidget(accounting_widget)
                self.content_stack.setCurrentWidget(accounting_widget)

            elif module_id == "reports":
                from src.ui.modules.reports import ReportsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ReportsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                reports_widget = ReportsWidget()
                self.content_stack.addWidget(reports_widget)
                self.content_stack.setCurrentWidget(reports_widget)

            elif module_id == "settings":
                from src.ui.modules.settings import SettingsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, SettingsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                settings_widget = SettingsWidget()
                self.content_stack.addWidget(settings_widget)
                self.content_stack.setCurrentWidget(settings_widget)

            elif module_id == "quotes":
                from src.ui.modules.quotes import QuotesWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, QuotesWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                quotes_widget = QuotesWidget()
                self.content_stack.addWidget(quotes_widget)
                self.content_stack.setCurrentWidget(quotes_widget)

            elif module_id == "orders":
                from src.ui.modules.orders import OrdersWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, OrdersWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                orders_widget = OrdersWidget()
                self.content_stack.addWidget(orders_widget)
                self.content_stack.setCurrentWidget(orders_widget)

            elif module_id == "barcode":
                from src.ui.modules.barcode_manager import BarcodeManagerWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, BarcodeManagerWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                barcode_widget = BarcodeManagerWidget()
                self.content_stack.addWidget(barcode_widget)
                self.content_stack.setCurrentWidget(barcode_widget)

            elif module_id == "import_export":
                from src.ui.modules.import_export import ImportExportWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ImportExportWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                import_export_widget = ImportExportWidget()
                self.content_stack.addWidget(import_export_widget)
                self.content_stack.setCurrentWidget(import_export_widget)

            elif module_id == "invoices":
                from src.ui.modules.invoices import InvoicesWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, InvoicesWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                invoices_widget = InvoicesWidget()
                self.content_stack.addWidget(invoices_widget)
                self.content_stack.setCurrentWidget(invoices_widget)

            elif module_id == "reports":
                from src.ui.modules.reports import ReportsWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ReportsWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                reports_widget = ReportsWidget()
                self.content_stack.addWidget(reports_widget)
                self.content_stack.setCurrentWidget(reports_widget)

            elif module_id == "accounting":
                from src.ui.modules.accounting import AccountingWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, AccountingWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                accounting_widget = AccountingWidget()
                self.content_stack.addWidget(accounting_widget)
                self.content_stack.setCurrentWidget(accounting_widget)

            elif module_id == "import_export":
                from src.ui.modules.import_export import ImportExportWidget

                for i in range(self.content_stack.count()):
                    widget = self.content_stack.widget(i)
                    if isinstance(widget, ImportExportWidget):
                        self.content_stack.setCurrentWidget(widget)
                        return

                import_export_widget = ImportExportWidget()
                self.content_stack.addWidget(import_export_widget)
                self.content_stack.setCurrentWidget(import_export_widget)

            elif module_id == "dashboard":
                # Retourner au tableau de bord (index 0)
                self.content_stack.setCurrentIndex(0)

            else:
                # Module non implémenté
                self.show_placeholder_module(module_id)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement du module {module_id}: {e}")
            self.show_error_message(f"Erreur lors du chargement du module: {e}")

    def show_placeholder_module(self, module_id):
        """Affiche un placeholder pour les modules non implémentés"""
        module_titles = {
            "dashboard": "Tableau de bord",
            "commercial": "Gestion Commerciale",
            "quotes": "Gestion des Devis",
            "orders": "Gestion des Commandes",
            "invoices": "Gestion des Factures",
            "clients": "Gestion des Clients",
            "suppliers": "Gestion des Fournisseurs",
            "products": "Gestion des Produits",
            "stock": "Gestion des Stocks",
            "inventory": "Inventaire",
            "barcode": "Gestion des Codes-barres",
            "accounting": "Comptabilité",
            "reports": "Rapports",
            "import_export": "Import / Export",
            "settings": "Paramètres",
        }

        title = module_titles.get(module_id, "Module")

        placeholder = QWidget()
        layout = QVBoxLayout(placeholder)
        layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel("🚧")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 64px;")
        layout.addWidget(icon_label)

        title_label = QLabel(f"{title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ffffff; margin: 20px;")
        layout.addWidget(title_label)

        desc_label = QLabel("Module en cours de développement")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("font-size: 16px; color: rgba(255, 255, 255, 0.7);")
        layout.addWidget(desc_label)

        self.content_stack.addWidget(placeholder)
        self.content_stack.setCurrentWidget(placeholder)

    def show_user_profile(self):
        """Affiche le profil utilisateur"""
        QMessageBox.information(self, "Profil", "Fonctionnalité de profil en cours de développement")

    def show_error_message(self, message):
        """Affiche un message d'erreur"""
        QMessageBox.critical(self, "Erreur", message)

    def apply_styles(self):
        """Applique les styles CSS avec couleurs sobres et professionnelles"""
        style = """
        QMainWindow {
            background-color: #1a1a2e;
            color: #ffffff;
        }

        #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2c3e50, stop:1 #1a252f);
            border-right: 2px solid rgba(52, 73, 94, 0.4);
            /* box-shadow: 2px 0 10px rgba(44, 62, 80, 0.3); */
        }

        #sidebarHeader {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(52, 73, 94, 0.3),
                stop:0.5 rgba(44, 62, 80, 0.2),
                stop:1 rgba(26, 37, 47, 0.15));
            border-bottom: 1px solid rgba(52, 73, 94, 0.4);
            border-radius: 0 0 15px 15px;
        }

        #logoLabel {
            font-size: 24px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3498db, stop:1 #5dade2);
            border-radius: 20px;
            text-align: center;
            border: 2px solid rgba(52, 152, 219, 0.4);
        }

        #titleLabel {
            font-size: 16px;
            font-weight: bold;
            color: #5dade2;
        }

        #subtitleLabel {
            font-size: 11px;
            color: rgba(93, 173, 226, 0.8);
        }

        #navButton {
            background: transparent;
            border: none;
            text-align: left;
            padding: 15px 20px;
            border-radius: 12px;
            margin: 4px 15px;
            min-height: 50px;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }

        #navButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(70, 130, 255, 0.25),
                stop:1 rgba(70, 130, 255, 0.15));
            border-left: 4px solid #4682ff;
            transform: translateX(3px);
        }

        #navButton:hover #navTitle {
            color: #ffffff;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        #navButton:hover #navIcon {
            color: #6fa8ff;
            transform: scale(1.1);
        }

        #navButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(52, 152, 219, 0.3),
                stop:1 rgba(93, 173, 226, 0.2));
        }

        #navIcon {
            font-size: 22px;
            color: #4682ff;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }

        #navTitle {
            font-size: 15px;
            color: #ffffff;
            font-weight: 600;
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.3px;
        }

        #userInfo {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(44, 62, 80, 0.4),
                stop:1 rgba(26, 37, 47, 0.6));
            border-top: 1px solid rgba(52, 73, 94, 0.4);
            margin: 12px;
            border-radius: 12px;
            border: 1px solid rgba(93, 173, 226, 0.3);
        }

        #userName {
            font-size: 14px;
            font-weight: bold;
            color: #5dade2;
        }

        #userActionButton {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(93, 173, 226, 0.3);
            border-radius: 15px;
            font-size: 14px;
            color: #5dade2;
        }

        #userActionButton:hover {
            background: rgba(93, 173, 226, 0.3);
            border-color: #5dade2;
        }

        #navScroll {
            background: transparent;
            border: none;
        }

        #navScroll QScrollBar:vertical {
            background: rgba(44, 62, 80, 0.3);
            width: 8px;
            border-radius: 4px;
            margin: 0;
        }

        #navScroll QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #5dade2);
            border-radius: 4px;
            min-height: 20px;
        }

        #navScroll QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5dade2, stop:1 #85c1e9);
        }

        #navScroll QScrollBar::add-line:vertical,
        #navScroll QScrollBar::sub-line:vertical {
            height: 0;
            background: transparent;
        }

        #contentArea {
            background: #1a1a2e;
        }
        """

        self.setStyleSheet(style)

    def toggle_theme(self):
        """Bascule entre les thèmes sombre et clair"""
        if THEME_MANAGER_AVAILABLE:
            theme_manager = get_theme_manager()
            current_theme = theme_manager.get_current_theme()
            new_theme = "light" if current_theme == "dark" else "dark"
            theme_manager.set_theme(new_theme)
            self.logger.info(f"Thème basculé vers: {new_theme}")
        else:
            QMessageBox.information(self, "Thème", "Gestionnaire de thème non disponible")

    def set_theme(self, theme_name):
        """Définit un thème spécifique"""
        if THEME_MANAGER_AVAILABLE:
            theme_manager = get_theme_manager()
            theme_manager.set_theme(theme_name)
            self.logger.info(f"Thème défini: {theme_name}")
        else:
            self.logger.warning("Gestionnaire de thème non disponible")

    def get_current_theme(self):
        """Retourne le thème actuel"""
        if THEME_MANAGER_AVAILABLE:
            theme_manager = get_theme_manager()
            return theme_manager.get_current_theme()
        return "dark"  # Thème par défaut

    def show_user_profile(self):
        """Affiche le profil utilisateur"""
        QMessageBox.information(self, "Profil", "Fonctionnalité en cours de développement")

    def show_color_settings(self):
        """Affiche le panneau de configuration des couleurs"""
        try:
            from src.ui.components.color_settings_panel import ColorSettingsPanel
            dialog = ColorSettingsPanel(self)
            dialog.settings_changed.connect(self.on_color_settings_changed)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "Configuration",
                "Panneau de configuration avancé non disponible")

    def on_color_settings_changed(self):
        """Gère les changements de paramètres de couleur"""
        self.logger.info("Paramètres de couleur mis à jour")
        # Réappliquer le thème si nécessaire
        if THEME_MANAGER_AVAILABLE:
            apply_unified_theme(self)

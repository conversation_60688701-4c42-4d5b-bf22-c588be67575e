#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire d'accessibilité GSCOM
Correction complète des problèmes de visibilité et contraste
"""

import os
import logging
from PyQt5.QtCore import QObject, pyqtSignal, QSettings
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont, QPalette, QColor

class AccessibilityManager(QObject):
    """Gestionnaire d'accessibilité pour GSCOM"""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings = QSettings("GSCOM", "AccessibilitySettings")
        
        # Charger les préférences
        self.current_theme = self.settings.value("theme", "light")
        self.high_contrast = self.settings.value("high_contrast", False, type=bool)
        self.large_text = self.settings.value("large_text", False, type=bool)
        
        # Charger le CSS d'accessibilité
        self.css_content = self.load_accessibility_css()
        
        self.logger.info(f"Gestionnaire d'accessibilité initialisé - Thème: {self.current_theme}")

    def load_accessibility_css(self):
        """Charge le fichier CSS d'accessibilité"""
        try:
            css_path = os.path.join(os.path.dirname(__file__), "gscom_accessibility_fix.css")
            if os.path.exists(css_path):
                with open(css_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                self.logger.warning("Fichier CSS d'accessibilité non trouvé")
                return self.get_fallback_css()
        except Exception as e:
            self.logger.error(f"Erreur chargement CSS: {e}")
            return self.get_fallback_css()

    def get_fallback_css(self):
        """CSS de secours avec contrastes optimaux"""
        return """
            /* CSS de secours - Mode clair accessible */
            QDialog, QMainWindow {
                background-color: #ffffff;
                color: #1e293b;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 16px;
            }
            
            #sidebar {
                background-color: #f1f5f9;
                border-right: 2px solid #e2e8f0;
                color: #1e293b;
            }
            
            #navButton {
                background-color: transparent;
                border: 2px solid transparent;
                color: #1e293b;
                padding: 16px;
                margin: 4px 16px;
                border-radius: 8px;
            }
            
            #navButton:hover {
                background-color: #f1f5f9;
                border-color: #2563eb;
                color: #2563eb;
            }
            
            #navIcon {
                color: #2563eb;
                font-size: 20px;
                font-weight: bold;
            }
            
            #navTitle {
                color: #1e293b;
                font-size: 16px;
                font-weight: 600;
            }
            
            QPushButton {
                background-color: #2563eb;
                border: 2px solid #2563eb;
                color: #ffffff;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 24px;
                border-radius: 8px;
                min-height: 40px;
            }
            
            QPushButton:hover {
                background-color: #3b82f6;
                border-color: #3b82f6;
            }
            
            QLineEdit, QTextEdit, QComboBox {
                background-color: #ffffff;
                border: 2px solid #e2e8f0;
                color: #1e293b;
                font-size: 16px;
                padding: 12px;
                border-radius: 8px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2563eb;
            }
        """

    def set_theme(self, theme_name):
        """Définit le thème avec sauvegarde"""
        if theme_name in ["light", "dark"]:
            self.current_theme = theme_name
            self.settings.setValue("theme", theme_name)
            self.apply_theme()
            self.theme_changed.emit(theme_name)
            self.logger.info(f"Thème changé vers: {theme_name}")
        else:
            self.logger.warning(f"Thème inconnu: {theme_name}")

    def toggle_theme(self):
        """Bascule entre thème clair et sombre"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.set_theme(new_theme)
        return new_theme

    def apply_theme(self):
        """Applique le thème à l'application"""
        app = QApplication.instance()
        if not app:
            self.logger.warning("Aucune application Qt trouvée")
            return

        # Construire le CSS complet
        css = self.build_complete_css()
        
        # Appliquer le CSS
        app.setStyleSheet(css)
        
        # Appliquer la classe de thème au widget principal
        for widget in app.topLevelWidgets():
            if hasattr(widget, 'setProperty'):
                widget.setProperty("class", f"{self.current_theme}-theme")
                if hasattr(widget, 'style'):
                    widget.style().unpolish(widget)
                    widget.style().polish(widget)
                widget.update()
        
        self.logger.info(f"Thème '{self.current_theme}' appliqué")

    def build_complete_css(self):
        """Construit le CSS complet avec modifications d'accessibilité"""
        css = self.css_content
        
        # Ajouter la classe de thème au body
        if self.current_theme == "dark":
            css = f".dark-theme {{ /* Thème sombre actif */ }}\n{css}"
        else:
            css = f".light-theme {{ /* Thème clair actif */ }}\n{css}"
        
        # Modifications pour contraste élevé
        if self.high_contrast:
            css += self.get_high_contrast_css()
        
        # Modifications pour texte agrandi
        if self.large_text:
            css += self.get_large_text_css()
        
        return css

    def get_high_contrast_css(self):
        """CSS pour contraste élevé"""
        return """
            /* Mode contraste élevé */
            * {
                border-width: 3px !important;
            }
            
            #navButton {
                border: 3px solid #000000 !important;
            }
            
            #navButton:hover {
                border: 3px solid #0000ff !important;
                background-color: #ffff00 !important;
                color: #000000 !important;
            }
            
            #navTitle {
                font-weight: bold !important;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;
            }
            
            QPushButton {
                border: 3px solid #000000 !important;
                font-weight: bold !important;
            }
        """

    def get_large_text_css(self):
        """CSS pour texte agrandi"""
        return """
            /* Mode texte agrandi */
            * {
                font-size: 18px !important;
            }
            
            #navTitle {
                font-size: 20px !important;
            }
            
            #navIcon {
                font-size: 24px !important;
            }
            
            QPushButton {
                font-size: 18px !important;
                padding: 16px 28px !important;
                min-height: 50px !important;
            }
            
            QLineEdit, QTextEdit, QComboBox {
                font-size: 18px !important;
                padding: 16px !important;
            }
        """

    def enable_high_contrast(self, enabled=True):
        """Active/désactive le mode contraste élevé"""
        self.high_contrast = enabled
        self.settings.setValue("high_contrast", enabled)
        self.apply_theme()
        self.logger.info(f"Contraste élevé: {'activé' if enabled else 'désactivé'}")

    def enable_large_text(self, enabled=True):
        """Active/désactive le mode texte agrandi"""
        self.large_text = enabled
        self.settings.setValue("large_text", enabled)
        self.apply_theme()
        self.logger.info(f"Texte agrandi: {'activé' if enabled else 'désactivé'}")

    def get_current_theme(self):
        """Retourne le thème actuel"""
        return self.current_theme

    def get_theme_display_name(self):
        """Retourne le nom d'affichage du thème"""
        names = {
            "light": "☀️ Clair",
            "dark": "🌙 Sombre"
        }
        return names.get(self.current_theme, "☀️ Clair")

    def apply_to_widget(self, widget):
        """Applique le thème à un widget spécifique"""
        if widget:
            css = self.build_complete_css()
            widget.setStyleSheet(css)
            widget.setProperty("class", f"{self.current_theme}-theme")
            if hasattr(widget, 'style'):
                widget.style().unpolish(widget)
                widget.style().polish(widget)
            widget.update()

    def get_accessibility_info(self):
        """Retourne les informations d'accessibilité"""
        return {
            "theme": self.current_theme,
            "high_contrast": self.high_contrast,
            "large_text": self.large_text,
            "css_loaded": bool(self.css_content)
        }

# Instance globale
_accessibility_manager = None

def get_accessibility_manager():
    """Retourne l'instance globale du gestionnaire d'accessibilité"""
    global _accessibility_manager
    if _accessibility_manager is None:
        _accessibility_manager = AccessibilityManager()
    return _accessibility_manager

def apply_accessible_theme(widget=None):
    """Applique le thème accessible"""
    manager = get_accessibility_manager()
    if widget:
        manager.apply_to_widget(widget)
    else:
        manager.apply_theme()

def set_accessible_theme(theme_name):
    """Définit le thème accessible"""
    manager = get_accessibility_manager()
    manager.set_theme(theme_name)

def toggle_accessible_theme():
    """Bascule le thème accessible"""
    manager = get_accessibility_manager()
    return manager.toggle_theme()

def enable_accessibility_features(high_contrast=False, large_text=False):
    """Active les fonctionnalités d'accessibilité"""
    manager = get_accessibility_manager()
    if high_contrast:
        manager.enable_high_contrast(True)
    if large_text:
        manager.enable_large_text(True)
